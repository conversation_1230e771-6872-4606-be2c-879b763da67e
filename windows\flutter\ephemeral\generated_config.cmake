# Generated code do not commit.
file(TO_CMAKE_PATH "G:\\Flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "G:\\BUtility - Almost - Copy" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=G:\\Flutter\\src\\flutter"
  "PROJECT_DIR=G:\\BUtility - Almost - Copy"
  "FLUTTER_ROOT=G:\\Flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=G:\\BUtility - Almost - Copy\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=G:\\BUtility - Almost - Copy"
  "FLUTTER_TARGET=G:\\BUtility - Almost - Copy\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=G:\\BUtility - Almost - Copy\\.dart_tool\\package_config.json"
)
