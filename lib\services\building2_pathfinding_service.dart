import '../models/building2_location.dart';

enum Building2NavigationDirection {
  forward,
  backward,
  reverse,
  upFloor,
  downFloor,
  turnLeft,
  turnRight,
  arrived,
}

class Building2NavigationStep {
  final Building2Location from;
  final Building2Location to;
  final Building2NavigationDirection direction;
  final String instruction;
  final int stepNumber;

  Building2NavigationStep({
    required this.from,
    required this.to,
    required this.direction,
    required this.instruction,
    required this.stepNumber,
  });

  @override
  String toString() => instruction;
}

class Building2NavigationRoute {
  final List<Building2NavigationStep> steps;
  final Building2Location destination;
  final int totalSteps;

  Building2NavigationRoute({
    required this.steps,
    required this.destination,
  }) : totalSteps = steps.length;

  bool get isEmpty => steps.isEmpty;
  bool get isNotEmpty => steps.isNotEmpty;

  Building2NavigationStep? getCurrentStep(Building2Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return steps[i];
      }
    }
    return null;
  }

  Building2NavigationStep? getNextStep(Building2Location currentLocation) {
    for (int i = 0; i < steps.length - 1; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return steps[i + 1];
      }
    }
    return null;
  }

  int getProgressPercentage(Building2Location currentLocation) {
    for (int i = 0; i < steps.length; i++) {
      if (steps[i].from.floor == currentLocation.floor &&
          steps[i].from.clage == currentLocation.clage) {
        return ((i / totalSteps) * 100).round();
      }
    }
    return 100; // Arrived
  }
}

class Building2PathfindingService {
  static const int _moveCost = 1;
  static const int _floorChangeCost = 2;
  static const int _reverseCost = 1;

  static Building2NavigationRoute? findShortestPath(
    Building2Location start,
    Building2Location destination, {
    bool isStartReversed = false,
  }) {
    if (start == destination) {
      return Building2NavigationRoute(steps: [], destination: destination);
    }

    // Use Dijkstra's algorithm for pathfinding
    final distances = <String, int>{};
    final previous = <String, Building2Location?>{};
    final unvisited = <Building2Location>[];

    // Initialize distances
    for (final location in Building2LocationData.allLocations) {
      distances[location.id] = double.maxFinite.toInt();
      previous[location.id] = null;
      unvisited.add(location);
    }
    distances[start.id] = 0;

    while (unvisited.isNotEmpty) {
      // Find unvisited node with minimum distance
      unvisited.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));
      final current = unvisited.removeAt(0);

      if (distances[current.id] == double.maxFinite.toInt()) break;
      if (current == destination) break;

      // Check all neighbors
      final neighbors = _getNeighbors(current);
      for (final neighbor in neighbors) {
        if (!unvisited.contains(neighbor)) continue;

        final cost = _calculateMoveCost(current, neighbor);
        final newDistance = distances[current.id]! + cost;

        if (newDistance < distances[neighbor.id]!) {
          distances[neighbor.id] = newDistance;
          previous[neighbor.id] = current;
        }
      }
    }

    // Reconstruct path
    final path = <Building2Location>[];
    Building2Location? current = destination;

    while (current != null) {
      path.insert(0, current);
      current = previous[current.id];
    }

    if (path.isEmpty || path.first != start) {
      return null; // No path found
    }

    // Convert path to navigation steps
    final steps = <Building2NavigationStep>[];
    bool userFacingReversed =
        isStartReversed; // Track user's current facing direction

    for (int i = 0; i < path.length - 1; i++) {
      final from = path[i];
      final to = path[i + 1];
      final previousLocation = i > 0 ? path[i - 1] : null;

      // Determine current facing direction
      bool isCurrentlyReversed = userFacingReversed;

      // Also check if the previous step was a floor change and we're not at clage0
      // Special handling for _r stairs at clage1
      if (!isCurrentlyReversed &&
          previousLocation != null &&
          previousLocation.floor != from.floor) {
        if (from.clage != 0) {
          // For main stairs (clage 9, 10), set to reversed after floor change
          if ((from.clage == 9 || from.clage == 10)) {
            isCurrentlyReversed = true;
            userFacingReversed = true;
          }
          // For _r stairs at clage1, maintain the reversed state if coming from _r stairs
          else if (from.clage == 1 && from.isReversed) {
            isCurrentlyReversed = true;
            userFacingReversed = true;
          }
          // For regular clage1 after coming from _r stairs, set to normal
          else if (from.clage == 1 &&
              !from.isReversed &&
              previousLocation.isReversed) {
            isCurrentlyReversed = false;
            userFacingReversed = false;
          }
        }
      }

      final direction = _getDirectionWithContext(
        from,
        to,
        previousLocation,
        isCurrentlyReversed,
        fullPath: path,
        currentStepIndex: i,
      );

      // If user turns around, update their facing direction for subsequent steps
      if (direction == Building2NavigationDirection.reverse) {
        userFacingReversed = !userFacingReversed;
      }

      final instruction = _getInstruction(from, to, direction);

      steps.add(Building2NavigationStep(
        from: from,
        to: to,
        direction: direction,
        instruction: instruction,
        stepNumber: i + 1,
      ));
    }

    return Building2NavigationRoute(steps: steps, destination: destination);
  }

  static List<Building2Location> _getNeighbors(Building2Location location) {
    final neighbors = <Building2Location>[];
    final maxClage = Building2LocationData.getMaxClageForFloor(location.floor);

    // Same floor movement
    if (location.clage > 0) {
      // Can move backward (clage - 1)
      final backward = Building2LocationData.findLocationByPosition(
          location.floor, location.clage - 1);
      if (backward != null) neighbors.add(backward);
    }

    if (location.clage < maxClage) {
      // Can move forward (clage + 1)
      final forward = Building2LocationData.findLocationByPosition(
          location.floor, location.clage + 1);
      if (forward != null) neighbors.add(forward);
    }

    // Allow movement between regular and reversed views at the same clage position
    // This is important for accessing the _r stairs from regular locations
    if (location.clage == 1 && location.floor >= 2) {
      // For clage 1 on floors 2, 3, and 4, allow switching between regular and reversed views
      final alternateView = Building2LocationData.findLocationByPosition(
          location.floor, location.clage,
          isReversed: !location.isReversed);
      if (alternateView != null) neighbors.add(alternateView);
    }

    // Floor changes (only from specific staircase locations)
    // Floor 1: clage 0 and clage 10 (stairs)
    // Floor 2: clage 10 (stairs) and clage 1 (both regular and reversed - stairs)
    // Floor 3: clage 9 (stairs) and clage 1 (both regular and reversed - stairs)
    // Floor 4: clage 9 (stairs) and clage 1 (both regular and reversed - stairs, down only for _r)
    bool canChangeFloors = false;
    if (location.floor == 1 && (location.clage == 0 || location.clage == 10)) {
      canChangeFloors = true;
    } else if (location.floor == 2 &&
        (location.clage == 10 || location.clage == 1)) {
      canChangeFloors = true;
    } else if (location.floor == 3 &&
        (location.clage == 9 || location.clage == 1)) {
      canChangeFloors = true;
    } else if (location.floor == 4 &&
        (location.clage == 9 || location.clage == 1)) {
      canChangeFloors = true;
    }

    if (canChangeFloors) {
      // Handle floor changes from different staircase locations

      // From Floor 1
      if (location.floor == 1 && location.clage == 10) {
        // From floor 1 clage 10, we can go to floor 2 clage 10
        final upFloor = Building2LocationData.findLocationByPosition(2, 10);
        if (upFloor != null) neighbors.add(upFloor);
      } else if (location.floor == 1 && location.clage == 1) {
        // From floor 1 clage 1, we can go up to floor 2 clage 1 (both regular and reversed)
        final upFloorRegular = Building2LocationData.findLocationByPosition(
            2, 1,
            isReversed: false);
        if (upFloorRegular != null) neighbors.add(upFloorRegular);
        final upFloorReversed = Building2LocationData.findLocationByPosition(
            2, 1,
            isReversed: true);
        if (upFloorReversed != null) neighbors.add(upFloorReversed);
      }

      // From Floor 2
      else if (location.floor == 2 && location.clage == 10) {
        // From floor 2 clage 10, we can go up to floor 3 clage 9 or down to floor 1 clage 10
        final upFloor = Building2LocationData.findLocationByPosition(3, 9);
        if (upFloor != null) neighbors.add(upFloor);
        final downFloor = Building2LocationData.findLocationByPosition(1, 10);
        if (downFloor != null) neighbors.add(downFloor);
      } else if (location.floor == 2 && location.clage == 1) {
        // From floor 2 clage 1 (regular or reversed), we can go up to floor 3 clage 1 or down to floor 1 clage 1
        final upFloorRegular = Building2LocationData.findLocationByPosition(
            3, 1,
            isReversed: false);
        if (upFloorRegular != null) neighbors.add(upFloorRegular);
        final upFloorReversed = Building2LocationData.findLocationByPosition(
            3, 1,
            isReversed: true);
        if (upFloorReversed != null) neighbors.add(upFloorReversed);
        final downFloor = Building2LocationData.findLocationByPosition(1, 1,
            isReversed: false);
        if (downFloor != null) neighbors.add(downFloor);
      }

      // From Floor 3
      else if (location.floor == 3 && location.clage == 9) {
        // From floor 3 clage 9, we can go up to floor 4 clage 9 or down to floor 2 clage 10
        final upFloor = Building2LocationData.findLocationByPosition(4, 9);
        if (upFloor != null) neighbors.add(upFloor);
        final downFloor = Building2LocationData.findLocationByPosition(2, 10);
        if (downFloor != null) neighbors.add(downFloor);
      } else if (location.floor == 3 && location.clage == 1) {
        // From floor 3 clage 1 (regular or reversed), we can go up to floor 4 clage 1 or down to floor 2 clage 1
        final upFloorRegular = Building2LocationData.findLocationByPosition(
            4, 1,
            isReversed: false);
        if (upFloorRegular != null) neighbors.add(upFloorRegular);
        final upFloorReversed = Building2LocationData.findLocationByPosition(
            4, 1,
            isReversed: true);
        if (upFloorReversed != null) neighbors.add(upFloorReversed);
        final downFloorRegular = Building2LocationData.findLocationByPosition(
            2, 1,
            isReversed: false);
        if (downFloorRegular != null) neighbors.add(downFloorRegular);
        final downFloorReversed = Building2LocationData.findLocationByPosition(
            2, 1,
            isReversed: true);
        if (downFloorReversed != null) neighbors.add(downFloorReversed);
      }

      // From Floor 4
      else if (location.floor == 4 && location.clage == 9) {
        // From floor 4 clage 9, we can only go down to floor 3 clage 9
        final downFloor = Building2LocationData.findLocationByPosition(3, 9);
        if (downFloor != null) neighbors.add(downFloor);
      } else if (location.floor == 4 && location.clage == 1) {
        // From floor 4 clage 1 (regular or reversed), we can only go down
        // 4clage1_r only allows downward movement, but 4clage1 regular can go down too
        final downFloorRegular = Building2LocationData.findLocationByPosition(
            3, 1,
            isReversed: false);
        if (downFloorRegular != null) neighbors.add(downFloorRegular);
        final downFloorReversed = Building2LocationData.findLocationByPosition(
            3, 1,
            isReversed: true);
        if (downFloorReversed != null) neighbors.add(downFloorReversed);
      }
    }

    return neighbors;
  }

  static int _calculateMoveCost(Building2Location from, Building2Location to) {
    if (from.floor != to.floor) {
      return _floorChangeCost;
    } else if (from.clage != to.clage) {
      return _moveCost;
    } else {
      return _reverseCost;
    }
  }

  static Building2NavigationDirection _getDirectionWithContext(
      Building2Location from,
      Building2Location to,
      Building2Location? previousLocation,
      bool isCurrentlyReversed,
      {List<Building2Location>? fullPath,
      int? currentStepIndex}) {
    // If this is a floor change, return the floor change direction
    if (from.floor != to.floor) {
      return to.floor > from.floor
          ? Building2NavigationDirection.upFloor
          : Building2NavigationDirection.downFloor;
    }

    // Determine if user is in reversed view
    bool isInReversedView = isCurrentlyReversed;

    // Also check if the previous step was a floor change and we're not at clage0
    // Special handling for _r stairs at clage1
    if (!isInReversedView &&
        previousLocation != null &&
        previousLocation.floor != from.floor) {
      if (from.clage != 0) {
        // For main stairs (clage 9, 10), set to reversed after floor change
        if ((from.clage == 9 || from.clage == 10)) {
          isInReversedView = true;
        }
        // For _r stairs at clage1, maintain the reversed state if coming from _r stairs
        else if (from.clage == 1 && from.isReversed) {
          isInReversedView = true;
        }
        // For regular clage1 after coming from _r stairs, set to normal
        else if (from.clage == 1 &&
            !from.isReversed &&
            previousLocation.isReversed) {
          isInReversedView = false;
        }
      }
    }

    // Check if we should suggest turning around for better navigation
    if (fullPath != null && currentStepIndex != null) {
      final shouldTurnAround = _shouldSuggestTurnAround(
          fullPath, currentStepIndex, isInReversedView);
      if (shouldTurnAround) {
        return Building2NavigationDirection.reverse;
      }
    }

    if (isInReversedView) {
      // In reversed view (_r images), the physical movement directions are inverted
      // When in reversed view, moving "forward" physically decreases clage numbers
      // and moving "backward" physically increases clage numbers
      if (to.clage > from.clage) {
        return Building2NavigationDirection
            .backward; // Need to move backward (physically) to go to higher clage
      } else if (to.clage < from.clage) {
        return Building2NavigationDirection
            .forward; // Need to move forward (physically) to go to lower clage
      }
    } else {
      // Normal view - directions match clage numbers directly
      // When in normal view, moving "forward" physically increases clage numbers
      // and moving "backward" physically decreases clage numbers
      if (to.clage > from.clage) {
        return Building2NavigationDirection
            .forward; // Moving to higher clage = forward (physically)
      } else if (to.clage < from.clage) {
        return Building2NavigationDirection
            .backward; // Moving to lower clage = backward (physically)
      }
    }

    return Building2NavigationDirection.reverse;
  }

  /// Determines if the user should turn around instead of moving backward
  /// for multiple consecutive steps in the same direction
  static bool _shouldSuggestTurnAround(List<Building2Location> fullPath,
      int currentStepIndex, bool isInReversedView) {
    if (currentStepIndex >= fullPath.length - 1) return false;

    final from = fullPath[currentStepIndex];
    final to = fullPath[currentStepIndex + 1];

    // Only consider turn-around for same-floor movements
    if (from.floor != to.floor) return false;

    // Determine what the natural movement direction would be
    bool wouldMoveBackward = false;
    if (isInReversedView) {
      // In reversed view, moving to higher clage requires moving backward
      wouldMoveBackward = to.clage > from.clage;
    } else {
      // In normal view, moving to lower clage requires moving backward
      wouldMoveBackward = to.clage < from.clage;
    }

    // If this step doesn't require moving backward, no need to turn around
    if (!wouldMoveBackward) return false;

    // Count consecutive backward movements in the same direction
    int consecutiveBackwardSteps = 0;
    int checkIndex = currentStepIndex;

    while (checkIndex < fullPath.length - 1) {
      final checkFrom = fullPath[checkIndex];
      final checkTo = fullPath[checkIndex + 1];

      // Stop if we change floors
      if (checkFrom.floor != checkTo.floor) break;

      // Check if this step also requires moving backward
      bool thisStepBackward = false;
      if (isInReversedView) {
        thisStepBackward = checkTo.clage > checkFrom.clage;
      } else {
        thisStepBackward = checkTo.clage < checkFrom.clage;
      }

      if (thisStepBackward) {
        consecutiveBackwardSteps++;
        checkIndex++;
      } else {
        break;
      }
    }

    // Suggest turning around if there are 2 or more consecutive backward steps
    return consecutiveBackwardSteps >= 2;
  }

  static String _getInstruction(Building2Location from, Building2Location to,
      Building2NavigationDirection direction) {
    switch (direction) {
      case Building2NavigationDirection.forward:
        return 'Move forward to ${to.displayName}';
      case Building2NavigationDirection.backward:
        return 'Move backward to ${to.displayName}';
      case Building2NavigationDirection.reverse:
        return 'Turn around and move forward to ${to.displayName}';
      case Building2NavigationDirection.upFloor:
        return 'Go up to Floor ${to.floor}';
      case Building2NavigationDirection.downFloor:
        return 'Go down to Floor ${to.floor}';
      case Building2NavigationDirection.turnLeft:
        return 'Turn left to ${to.displayName}';
      case Building2NavigationDirection.turnRight:
        return 'Turn right to ${to.displayName}';
      case Building2NavigationDirection.arrived:
        return 'You have arrived at ${to.displayName}';
    }
  }

  static String getDetailedInstruction(Building2NavigationStep step) {
    final direction = step.direction;
    final to = step.to;

    switch (direction) {
      case Building2NavigationDirection.forward:
        return 'Move forward to ${to.displayName}';
      case Building2NavigationDirection.backward:
        return 'Move backward to ${to.displayName}';
      case Building2NavigationDirection.upFloor:
        return 'Take the stairs up to Floor ${to.floor}';
      case Building2NavigationDirection.downFloor:
        return 'Take the stairs down to Floor ${to.floor}';
      case Building2NavigationDirection.reverse:
        return 'Turn around and move forward to ${to.displayName}';
      default:
        return step.instruction;
    }
  }
}
