import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_init;
import 'package:test_1_copy_auth_events/services/local_database_helper.dart';

class OfflineNotificationService {
  static final OfflineNotificationService _instance =
      OfflineNotificationService._internal();
  factory OfflineNotificationService() => _instance;
  OfflineNotificationService._internal();

  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final LocalDatabaseHelper _localDb = LocalDatabaseHelper();

  Timer? _notificationTimer;
  bool _isInitialized = false;

  // Set to track notification IDs that have been sent to avoid duplicates
  final Set<String> _sentNotifications = {};

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone data
    tz_init.initializeTimeZones();

    // Initialize notification settings
    const AndroidInitializationSettings initSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initSettingsAndroid,
      iOS: initSettingsIOS,
    );

    await notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) {},
    );

    _isInitialized = true;

    // Start the periodic checker
    startPeriodicChecks();
  }

  // Start checking for notifications periodically
  void startPeriodicChecks() {
    // Cancel any existing timer
    _notificationTimer?.cancel();

    // Set up a timer that runs every 15 seconds
    _notificationTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      checkForNotifications();
    });
  }

  // Stop periodic checks
  void stopPeriodicChecks() {
    _notificationTimer?.cancel();
    _notificationTimer = null;
  }

  // Main method to check for all types of notifications
  Future<void> checkForNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail');

    // Skip if user is not logged in
    if (userEmail == null) return;

    try {
      // Get tasks from local database
      final localTasks = await _localDb.getTasks(userEmail);

      // Convert to the format expected by notification methods
      List<Map<String, dynamic>> tasks = localTasks.map((task) {
        DateTime? deadlineDate;
        if (task['deadline'] != null &&
            task['deadline'].toString().isNotEmpty) {
          try {
            deadlineDate = DateTime.parse(task['deadline'].toString());
          } catch (e) {
            print('Error parsing deadline date: ${task['deadline']} - $e');
          }
        }

        DateTime? notifyDate;
        if (task['notify'] != null && task['notify'].toString().isNotEmpty) {
          try {
            notifyDate = DateTime.parse(task['notify'].toString());
          } catch (e) {
            print('Error parsing notify date: ${task['notify']} - $e');
          }
        }

        return {
          'text': task['task'],
          'isChecked': task['completed'] == 'y',
          'deadlineDate': deadlineDate,
          'notifyDate': notifyDate,
        };
      }).toList();

      // Check for different types of notifications
      await checkForDueNotifications(tasks);
      await checkForReminderNotifications(tasks);
      await checkForOngoingTasksNotification(tasks);
      await checkForUpcomingDueNotifications(tasks);
      await checkForOverdueNotifications(tasks);
    } catch (e) {
      print('Error checking for offline notifications: $e');
    }
  }

  // Check for tasks due today
  Future<void> checkForDueNotifications(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Create a unique ID for today's notification to avoid duplicates
    final notifyId = 'due_today_${today.year}${today.month}${today.day}';

    // Skip if we've already sent this notification today
    if (_sentNotifications.contains(notifyId)) return;

    final dueTasks = tasks.where((task) {
      if (task['isChecked'] == true || task['deadlineDate'] == null)
        return false;

      final deadlineDate = task['deadlineDate'] as DateTime;
      final taskDate =
          DateTime(deadlineDate.year, deadlineDate.month, deadlineDate.day);

      return taskDate.isAtSameMomentAs(today);
    }).toList();

    if (dueTasks.isNotEmpty) {
      String taskNames = dueTasks.map((t) => t['text']).join(', ');
      String message = dueTasks.length == 1
          ? '"$taskNames" is due today! Please make sure to complete it asap!⏳'
          : 'You’ve got ${dueTasks.length} tasks due today: $taskNames. Let’s do this!';

      await showNotification(
        id: 101,
        title: 'BUtility: Due Tasks',
        body: message,
        channelId: 'offline_due_channel',
        channelName: 'Offline Due Tasks',
        channelDescription: 'Notifications for tasks due today (offline)',
        color: Colors.red,
      );

      _sentNotifications.add(notifyId);

      // Clean up the set after 24 hours
      Timer(const Duration(hours: 24), () {
        _sentNotifications.remove(notifyId);
      });
    }
  }

  // Check for tasks with reminders
  Future<void> checkForReminderNotifications(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();

    // Filter for uncompleted tasks with notify dates that match current time
    final dueTasks = tasks.where((task) {
      if (task['isChecked'] == true || task['notifyDate'] == null) return false;

      final notifyDate = task['notifyDate'] as DateTime;
      final taskText = task['text'] ?? 'Task';
      final notifyId =
          'notify_${taskText.hashCode}_${notifyDate.year}${notifyDate.month}${notifyDate.day}${notifyDate.hour}${notifyDate.minute}';

      // Check if notification time matches current time (within the minute)
      // AND we haven't already sent this notification
      if (notifyDate.year == now.year &&
          notifyDate.month == now.month &&
          notifyDate.day == now.day &&
          notifyDate.hour == now.hour &&
          notifyDate.minute == now.minute) {
        // Only return true if we haven't sent this notification yet
        if (!_sentNotifications.contains(notifyId)) {
          _sentNotifications.add(notifyId);

          // Clean up the set after some time
          Timer(const Duration(minutes: 2), () {
            _sentNotifications.remove(notifyId);
          });

          return true;
        }
      }

      return false;
    }).toList();

    // Show notifications for due tasks
    for (var task in dueTasks) {
      await showTaskNotification(task);
    }
  }

  // Check for ongoing tasks notification (once per day)
  Future<void> checkForOngoingTasksNotification(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Create a unique ID for today's notification to avoid duplicates
    final notifyId = 'ongoing_tasks_${today.year}${today.month}${today.day}';

    // Skip if we've already sent this notification today
    if (_sentNotifications.contains(notifyId)) return;

    // Only show this notification once per day, at 9 AM
    if (now.hour == 9 && now.minute >= 0 && now.minute < 5) {
      int ongoingCount = tasks.where((t) => t['isChecked'] == false).length;

      String message = ongoingCount > 0
          ? 'You’re working on $ongoingCount task${ongoingCount != 1 ? 's' : ''}. You’ve got this! 💪'
          : 'Nice work! All tasks completed.';

      await showNotification(
        id: 102,
        title: 'BUtility: Ongoing Tasks',
        body: message,
        channelId: 'offline_ongoing_channel',
        channelName: 'Offline Ongoing Tasks',
        channelDescription: 'Notifications for ongoing tasks (offline)',
        color: Colors.green,
      );

      _sentNotifications.add(notifyId);

      // Clean up the set after 24 hours
      Timer(const Duration(hours: 24), () {
        _sentNotifications.remove(notifyId);
      });
    }
  }

  // Check for tasks approaching their deadline (30 minutes before)
  Future<void> checkForUpcomingDueNotifications(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();

    // Process each task
    for (var task in tasks) {
      if (task['isChecked'] == true || task['deadlineDate'] == null) continue;

      final deadlineDate = task['deadlineDate'] as DateTime;
      final taskText = task['text'] ?? 'Task';

      // Create unique identifier for this task's notification
      final taskId =
          '${taskText.hashCode}_${deadlineDate.millisecondsSinceEpoch}';
      final upcomingId = 'upcoming_$taskId';

      // Calculate time difference in minutes
      final differenceInMinutes = deadlineDate.difference(now).inMinutes;

      // Task is due in 30 minutes (with a small window of 1 minute to catch it)
      if (differenceInMinutes >= 29 && differenceInMinutes <= 30) {
        // Check if we haven't sent this notification yet
        if (!_sentNotifications.contains(upcomingId)) {
          print(
              'Detected upcoming task: $taskText due in $differenceInMinutes minutes');

          // Add to sent notifications immediately to prevent duplicates
          _sentNotifications.add(upcomingId);

          // Wait 5 seconds before showing the notification
          await Future.delayed(const Duration(seconds: 5));

          print(
              'Showing upcoming notification after delay for task: $taskText');
          await showUpcomingDueNotification(task);

          // Clean up the set after some time to prevent memory leaks
          Timer(const Duration(minutes: 60), () {
            _sentNotifications.remove(upcomingId);
          });
        }
      }
    }
  }

  // Check for overdue tasks (5 minutes after deadline)
  Future<void> checkForOverdueNotifications(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();

    // Process each task
    for (var task in tasks) {
      if (task['isChecked'] == true || task['deadlineDate'] == null) continue;

      final deadlineDate = task['deadlineDate'] as DateTime;
      final taskText = task['text'] ?? 'Task';

      // Create unique identifier for this task's notification
      final taskId =
          '${taskText.hashCode}_${deadlineDate.millisecondsSinceEpoch}';
      final overdueId = 'overdue_$taskId';

      // Calculate time difference in minutes (will be negative for overdue tasks)
      final differenceInMinutes = deadlineDate.difference(now).inMinutes;

      // Task is 5 minutes overdue (with a small window of 1 minute to catch it)
      if (differenceInMinutes <= -5 && differenceInMinutes >= -6) {
        // Check if we haven't sent this notification yet
        if (!_sentNotifications.contains(overdueId)) {
          print(
              'Detected overdue task: $taskText overdue by ${-differenceInMinutes} minutes');

          // Add to sent notifications immediately to prevent duplicates
          _sentNotifications.add(overdueId);

          // Wait 5 seconds before showing the notification
          await Future.delayed(const Duration(seconds: 5));

          print('Showing overdue notification after delay for task: $taskText');
          await showOverdueNotification(task);

          // Clean up the set after some time
          Timer(const Duration(minutes: 60), () {
            _sentNotifications.remove(overdueId);
          });
        }
      }
    }
  }

  // Show notification for a specific task reminder
  Future<void> showTaskNotification(Map<String, dynamic> task) async {
    final String taskText = task['text'] ?? 'Task reminder';

    // Always show the reminder message for notifications triggered by notifyDate
    String notificationBody = 'You’ve got a task waiting—make it happen!⏰';

    // Only add deadline information if it exists and is in the future
    if (task['deadlineDate'] != null) {
      final deadlineDate = task['deadlineDate'] as DateTime;
      final now = DateTime.now();

      // Only mention the deadline if it's in the future
      if (deadlineDate.isAfter(now)) {
        final formattedDate =
            '${deadlineDate.month}/${deadlineDate.day} at ${_formatTime(deadlineDate)}';
        notificationBody =
            'You’ve got a task waiting—make it happen!⏰ (Due on $formattedDate)';
      }
    }

    await showNotification(
      title: 'BUtility: Notify Tasks - "$taskText"',
      body: notificationBody,
      channelId: 'offline_reminder_channel',
      channelName: 'Offline Reminders',
      channelDescription: 'Notifications for task reminders (offline)',
      color: Colors.blue,
    );
  }

  // Show notification for tasks due in 30 minutes
  Future<void> showUpcomingDueNotification(Map<String, dynamic> task) async {
    final String taskText = task['text'] ?? 'Task reminder';
    final deadlineDate = task['deadlineDate'] as DateTime;
    final formattedTime = _formatTime(deadlineDate);

    String notificationBody =
        'Task is due in 30 minutes at $formattedTime! Make sure you finish it asap!⏳';

    await showNotification(
      title: 'BUtility: Upcoming Task - "$taskText"',
      body: notificationBody,
      channelId: 'offline_upcoming_due_channel',
      channelName: 'Offline Upcoming Due Tasks',
      channelDescription: 'Notifications for tasks due in 30 minutes (offline)',
      color: Colors.orange,
    );
  }

  // Show notification for overdue tasks
  Future<void> showOverdueNotification(Map<String, dynamic> task) async {
    final String taskText = task['text'] ?? 'Task reminder';
    final deadlineDate = task['deadlineDate'] as DateTime;
    final formattedTime = _formatTime(deadlineDate);

    String notificationBody =
        'Task was due at $formattedTime and is now overdue!⚠️';

    await showNotification(
      title: 'BUtility: Overdue Task - "$taskText"',
      body: notificationBody,
      channelId: 'offline_overdue_channel',
      channelName: 'Offline Overdue Tasks',
      channelDescription: 'Notifications for tasks that are overdue (offline)',
      color: Colors.red,
    );
  }

  // Helper method to format time
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12
        ? dateTime.hour - 12
        : (dateTime.hour == 0 ? 12 : dateTime.hour);
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute $period';
  }

  // Generic notification method with customizable channel
  Future<void> showNotification({
    int id = 0,
    String? title,
    String? body,
    String channelId = 'offline_task_channel',
    String channelName = 'Offline Tasks',
    String channelDescription = 'Channel for offline task notifications',
    Color color = Colors.blue,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      color: color,
      colorized: true,
    );

    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Generate a unique ID for each notification based on current time
    final uniqueId =
        id == 0 ? DateTime.now().millisecondsSinceEpoch % 10000 : id;

    await notificationsPlugin.show(
      uniqueId,
      title,
      body,
      details,
    );
  }

  // Clean up resources
  void dispose() {
    stopPeriodicChecks();
    _sentNotifications.clear();
  }
}
