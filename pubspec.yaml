name: test_1_copy_auth_events
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  google_nav_bar: ^5.0.6
  #hive: ^2.0.0
  # hive_flutter: ^1.0.0
  #path_provider: ^2.1.5
  image_picker: ^1.1.2
  shared_preferences: ^2.2.2
  scrollable_positioned_list: ^0.3.8
  flutter_local_notifications: ^18.0.1
  supabase_flutter: ^2.8.3
  path_provider: ^2.0.15
  http: ^1.1.0
  url_launcher: ^6.3.1
  supabase: ^2.6.3
  intl: ^0.20.2
  table_calendar: ^3.2.0
  timezone: ^0.10.0
  workmanager: ^0.5.2
  android_alarm_manager_plus: ^4.0.7
  flutter_background_service_android: ^6.3.0
  flutter_background_service: ^5.1.0
  cached_network_image: ^3.4.1
  sqflite: ^2.3.0
  connectivity_plus: ^6.1.4
  path: ^1.8.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/BUtility.jpg
    - assets/BUtility_EventSample.jpg
    - assets/BUtility_EventSample1.jpg
    - assets/BUtility_EventSample2.jpg
    - assets/QWER_Chodan.jpg
    - assets/NAV/ANNEX.jpg
    - assets/NAV/MAIN.jpg
    - assets/NAV/MAP.png
    - assets/CITE/cite0.jpg
    - assets/CITE/cite1.jpg
    - assets/CITE/cite1_r.jpg
    - assets/CITE/cite2.jpg
    - assets/CITE/cite2_r.jpg
    - assets/CITE/cite3.jpg
    - assets/CITE/cite3_r.jpg
    - assets/CITE/cite4.jpg
    - assets/CITE/cite4_r.jpg
    - assets/CITE/2cite0.jpg
    - assets/CITE/2cite1.jpg
    - assets/CITE/2cite1_r.jpg
    - assets/CITE/2cite2.jpg
    - assets/CITE/2cite2_r.jpg
    - assets/CITE/2cite3.jpg
    - assets/CITE/2cite3_r.jpg
    - assets/CITE/2cite4.jpg
    - assets/CITE/2cite4_r.jpg
    - assets/CITE/3cite0.jpg
    - assets/CITE/3cite1.jpg
    - assets/CITE/3cite1_r.jpg
    - assets/CITE/3cite2.jpg
    - assets/CITE/3cite2_r.jpg
    - assets/CITE/3cite3.jpg
    - assets/CITE/3cite3_r.jpg
    - assets/CITE/3cite4.jpg
    - assets/CITE/3cite4_r.jpg
    - assets/CITE/4cite0.jpg
    - assets/CITE/4cite1.jpg
    - assets/CITE/4cite1_r.jpg
    - assets/CITE/4cite2.jpg
    - assets/CITE/4cite2_r.jpg
    - assets/CITE/4cite3.jpg
    - assets/CITE/4cite3_r.jpg
    - assets/CITE/4cite4.jpg
    - assets/CITE/4cite4_r.jpg
    - assets/CLAGE/clage0.jpg
    - assets/CLAGE/clage1_r.jpg
    - assets/CLAGE/clage1.jpg
    - assets/CLAGE/clage2_r.jpg
    - assets/CLAGE/clage2.jpg
    - assets/CLAGE/clage3_r.jpg
    - assets/CLAGE/clage3.jpg
    - assets/CLAGE/clage4_r.jpg
    - assets/CLAGE/clage4.jpg
    - assets/CLAGE/clage5_r.jpg
    - assets/CLAGE/clage5.jpg
    - assets/CLAGE/clage6_r.jpg
    - assets/CLAGE/clage6.jpg
    - assets/CLAGE/clage7_r.jpg
    - assets/CLAGE/clage7.jpg
    - assets/CLAGE/clage8_r.jpg
    - assets/CLAGE/clage8.jpg
    - assets/CLAGE/clage9_r.jpg
    - assets/CLAGE/clage9.jpg
    - assets/CLAGE/clage10_r.jpg
    - assets/CLAGE/clage10.jpg
    - assets/CLAGE/2clage1_r.jpg
    - assets/CLAGE/2clage1.jpg
    - assets/CLAGE/2clage2_r.jpg
    - assets/CLAGE/2clage2.jpg
    - assets/CLAGE/2clage3_r.jpg
    - assets/CLAGE/2clage3.jpg
    - assets/CLAGE/2clage4_r.jpg
    - assets/CLAGE/2clage4.jpg
    - assets/CLAGE/2clage5_r.jpg
    - assets/CLAGE/2clage5.jpg
    - assets/CLAGE/2clage6_r.jpg
    - assets/CLAGE/2clage6.jpg
    - assets/CLAGE/2clage7_r.jpg
    - assets/CLAGE/2clage7.jpg
    - assets/CLAGE/2clage8_r.jpg
    - assets/CLAGE/2clage8.jpg
    - assets/CLAGE/2clage9_r.jpg
    - assets/CLAGE/2clage9.jpg
    - assets/CLAGE/2clage10_r.jpg
    - assets/CLAGE/2clage10.jpg
    - assets/CLAGE/3clage1_r.jpg
    - assets/CLAGE/3clage1.jpg
    - assets/CLAGE/3clage2_r.jpg
    - assets/CLAGE/3clage2.jpg
    - assets/CLAGE/3clage3_r.jpg
    - assets/CLAGE/3clage3.jpg
    - assets/CLAGE/3clage4_r.jpg
    - assets/CLAGE/3clage4.jpg
    - assets/CLAGE/3clage5_r.jpg
    - assets/CLAGE/3clage5.jpg
    - assets/CLAGE/3clage6_r.jpg
    - assets/CLAGE/3clage6.jpg
    - assets/CLAGE/3clage7_r.jpg
    - assets/CLAGE/3clage7.jpg
    - assets/CLAGE/3clage8_r.jpg
    - assets/CLAGE/3clage8.jpg
    - assets/CLAGE/3clage9_r.jpg
    - assets/CLAGE/3clage9.jpg
    - assets/CLAGE/4clage1_r.jpg
    - assets/CLAGE/4clage1.jpg
    - assets/CLAGE/4clage2_r.jpg
    - assets/CLAGE/4clage2.jpg
    - assets/CLAGE/4clage3_r.jpg
    - assets/CLAGE/4clage3.jpg
    - assets/CLAGE/4clage4_r.jpg
    - assets/CLAGE/4clage4.jpg
    - assets/CLAGE/4clage5_r.jpg
    - assets/CLAGE/4clage5.jpg
    - assets/CLAGE/4clage6_r.jpg
    - assets/CLAGE/4clage6.jpg
    - assets/CLAGE/4clage7_r.jpg
    - assets/CLAGE/4clage7.jpg
    - assets/CLAGE/4clage8_r.jpg
    - assets/CLAGE/4clage8.jpg
    - assets/CLAGE/4clage9_r.jpg
    - assets/CLAGE/4clage9.jpg
    - assets/MAIN/2main1.jpg
    - assets/MAIN/2main1_r.jpg
    - assets/MAIN/2main2.jpg
    - assets/MAIN/2main2_r.jpg
    - assets/MAIN/2main3.jpg
    - assets/MAIN/2main3_r.jpg
    - assets/MAIN/2main4.jpg
    - assets/MAIN/2main4_r.jpg
    - assets/MAIN/2main5.jpg
    - assets/MAIN/2main5_r.jpg
    - assets/MAIN/2main6.jpg
    - assets/MAIN/2main6_r.jpg
    - assets/MAIN/2main7.jpg
    - assets/MAIN/2main7_r.jpg
    - assets/MAIN/2main8.jpg
    - assets/MAIN/2main8_r.jpg
    - assets/MAIN/2main9.jpg
    - assets/MAIN/2main9_r.jpg
    - assets/MAIN/main1.jpg
    - assets/MAIN/main1_r.jpg
    - assets/MAIN/main2.jpg
    - assets/MAIN/main2_r.jpg
    - assets/MAIN/main3.jpg
    - assets/MAIN/main3_r.jpg
    - assets/MAIN/main4.jpg
    - assets/MAIN/main4_r.jpg
    - assets/MAIN/main5.jpg
    - assets/MAIN/main5_r.jpg
    - assets/MAIN/main6.jpg
    - assets/MAIN/main6_r.jpg
    - assets/MAIN/main7.jpg
    - assets/MAIN/main7_r.jpg
    - assets/MAIN/main8.jpg
    - assets/MAIN/main8_r.jpg
    - assets/MAIN/main9.jpg
    - assets/MAIN/main9_r.jpg
    - assets/MAIN/main10.jpg
    - assets/MAIN/main10_r.jpg
    - assets/MAIN/main11.jpg
    - assets/MAIN/main11_r.jpg
    - assets/MAIN/main12.jpg
    - assets/MAIN/main12_r.jpg
    - assets/MAIN/main13.jpg
    - assets/MAIN/main13_r.jpg
    - assets/MAIN/main14.jpg
    - assets/MAIN/main14_r.jpg
    - assets/MAIN/main15.jpg
    - assets/MAIN/main15_r.jpg
    - assets/MAIN/main16.jpg
    - assets/MAIN/main17.jpg
    - assets/MAIN/main17_r.jpg
    - assets/MAIN/main18.jpg
    - assets/MAIN/main18_r.jpg
    - assets/MAIN/main19.jpg
    - assets/MAIN/main19_r.jpg
    - assets/MAIN/main20.jpg
    - assets/MAIN/main20_r.jpg
    - assets/MAIN/main21.jpg
    - assets/MAIN/main21_r.jpg
    - assets/MAIN/main22_r.jpg
    - assets/MAIN/main22.jpg
    - assets/MAIN/main23_r.jpg
    - assets/MAIN/main23.jpg
    - assets/MAIN/main24_r.jpg
    - assets/MAIN/main24.jpg
    - assets/MAIN/main25_r.jpg
    - assets/MAIN/main25.jpg
    - assets/MAIN/3main0.jpg
    - assets/MAIN/3main1_r.jpg
    - assets/MAIN/3main1.jpg
    - assets/MAIN/main0.jpg
    - assets/MAIN/main26.jpg
    - assets/MAIN/main26_r.jpg
    - assets/IMAGES/accounting.jpg
    - assets/IMAGES/alumni.JPG
    - assets/IMAGES/bahaynabato.jpg
    - assets/IMAGES/bookstore.jpg
    - assets/IMAGES/canteen.jpg
    - assets/IMAGES/clinic.jpg
    - assets/IMAGES/csc.jpg
    - assets/IMAGES/finance.jpg
    - assets/IMAGES/claw.jpg
    - assets/IMAGES/registrar.jpg

  
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
