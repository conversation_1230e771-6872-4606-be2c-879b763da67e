import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  print('=== Testing Building 2 Pathfinding with Faster Stairs ===\n');

  // Test case: From Human Resources Department (2clage1) to Chemistry Lab 2 (3clage2)
  // This should now use the faster route via the _r stairs

  // Find the locations
  final humanResources = Building2LocationData.findLocationByPosition(2, 1, isReversed: false);
  final chemLab2 = Building2LocationData.findLocationByPosition(3, 2, isReversed: false);

  if (humanResources == null || chemLab2 == null) {
    print('Error: Could not find locations');
    print('HR: $humanResources');
    print('Chem Lab 2: $chemLab2');
    return;
  }

  print('Testing pathfinding from ${humanResources.displayName} to ${chemLab2.displayName}');
  print('Expected: Should use the nearby _r stairs for faster navigation\n');

  // Find the shortest path
  final route = Building2PathfindingService.findShortestPath(humanResources, chemLab2);

  if (route == null) {
    print('Error: No route found');
    return;
  }

  print('Route found with ${route.totalSteps} steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
    print('   From: ${step.from.displayName} (Floor ${step.from.floor}, clage ${step.from.clage}${step.from.isReversed ? '_r' : ''})');
    print('   To: ${step.to.displayName} (Floor ${step.to.floor}, clage ${step.to.clage}${step.to.isReversed ? '_r' : ''})');
    print('');
  }

  // Check if the route uses the _r stairs
  bool usesRStairs = route.steps.any((step) =>
    (step.from.isReversed && step.from.clage == 1) ||
    (step.to.isReversed && step.to.clage == 1)
  );

  print('Uses _r stairs: ${usesRStairs ? "YES ✓" : "NO ✗"}');

  if (usesRStairs) {
    print('SUCCESS: The pathfinding is now using the faster _r stairs!');
  } else {
    print('ISSUE: The pathfinding is still not using the _r stairs.');
  }
}
