import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  print('=== Testing Building 2 Pathfinding Direction Fix ===\n');

  // Test case: From Chemistry Lab 1 (3clage1_r) to Psychology Lab (2clage6)
  // This should go down stairs and then move correctly without "turn around"

  // Find the locations
  final chemLab1R =
      Building2LocationData.findLocationByPosition(3, 1, isReversed: true);
  final psychLab =
      Building2LocationData.findLocationByPosition(2, 6, isReversed: false);

  if (chemLab1R == null || psychLab == null) {
    print('Error: Could not find locations');
    print('Chem Lab 1_r: $chemLab1R');
    print('Psych Lab: $psychLab');
    return;
  }

  print(
      'Testing pathfinding from ${chemLab1R.displayName} (3clage1_r) to ${psychLab.displayName}');
  print(
      'Expected: Go down stairs, then move correctly without "turn around"\n');

  // Find the shortest path
  final route =
      Building2PathfindingService.findShortestPath(chemLab1R, psychLab);

  if (route == null) {
    print('Error: No route found');
    return;
  }

  print('Route found with ${route.totalSteps} steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
    print(
        '   From: ${step.from.displayName} (Floor ${step.from.floor}, clage ${step.from.clage}${step.from.isReversed ? '_r' : ''})');
    print(
        '   To: ${step.to.displayName} (Floor ${step.to.floor}, clage ${step.to.clage}${step.to.isReversed ? '_r' : ''})');
    print('');
  }

  // Check if the route uses the _r stairs
  bool usesRStairs = route.steps.any((step) =>
      (step.from.isReversed && step.from.clage == 1) ||
      (step.to.isReversed && step.to.clage == 1));

  print('Uses _r stairs: ${usesRStairs ? "YES ✓" : "NO ✗"}');

  if (usesRStairs) {
    print('SUCCESS: The pathfinding is now using the faster _r stairs!');
  } else {
    print('ISSUE: The pathfinding is still not using the _r stairs.');
  }
}
