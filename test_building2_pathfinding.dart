import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  print('=== Testing Building 2 Pathfinding Direction Fix ===\n');

  // Test case: From Human Resources (2clage1_r) to Chemistry Center for Science (3clage?)
  // This should go up stairs and then move correctly without "turn around"

  // Find the locations
  final humanResourcesR =
      Building2LocationData.findLocationByPosition(2, 1, isReversed: true);

  // Find Chemistry Center for Science - let's search for it
  final chemCenterResults =
      Building2LocationData.searchLocations('chemistry center');
  print(
      'Found chemistry center locations: ${chemCenterResults.map((l) => l.displayName).join(', ')}');

  // Use Chemistry Laboratory 2 as target (floor 3, clage 2)
  final chemCenter =
      Building2LocationData.findLocationByPosition(3, 2, isReversed: false);

  if (humanResourcesR == null || chemCenter == null) {
    print('Error: Could not find locations');
    print('HR_r: $humanResourcesR');
    print('Chem Center: $chemCenter');
    return;
  }

  print(
      'Testing pathfinding from ${humanResourcesR.displayName} (2clage1_r) to ${chemCenter.displayName}');
  print('Expected: Go up stairs, then move correctly without "turn around"\n');

  // Find the shortest path
  final route =
      Building2PathfindingService.findShortestPath(humanResourcesR, chemCenter);

  if (route == null) {
    print('Error: No route found');
    return;
  }

  print('Route found with ${route.totalSteps} steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
    print(
        '   From: ${step.from.displayName} (Floor ${step.from.floor}, clage ${step.from.clage}${step.from.isReversed ? '_r' : ''})');
    print(
        '   To: ${step.to.displayName} (Floor ${step.to.floor}, clage ${step.to.clage}${step.to.isReversed ? '_r' : ''})');
    print('');
  }

  // Check if the route uses the _r stairs
  bool usesRStairs = route.steps.any((step) =>
      (step.from.isReversed && step.from.clage == 1) ||
      (step.to.isReversed && step.to.clage == 1));

  print('Uses _r stairs: ${usesRStairs ? "YES ✓" : "NO ✗"}');

  if (usesRStairs) {
    print('SUCCESS: The pathfinding is now using the faster _r stairs!');
  } else {
    print('ISSUE: The pathfinding is still not using the _r stairs.');
  }
}
