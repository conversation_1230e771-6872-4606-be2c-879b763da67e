import 'lib/models/building2_location.dart';
import 'lib/services/building2_pathfinding_service.dart';

void main() {
  // Test case: From Human Resources Department (2clage1) to Chemistry Lab 2 (3clage2)
  // This should now use the faster route via the _r stairs
  
  // Find the locations
  final humanResources = Building2LocationData.findLocationByPosition(2, 1, isReversed: false);
  final chemLab2 = Building2LocationData.findLocationByPosition(3, 2, isReversed: false);
  
  if (humanResources == null || chemLab2 == null) {
    print('Error: Could not find locations');
    return;
  }
  
  print('Testing pathfinding from ${humanResources.displayName} to ${chemLab2.displayName}');
  
  // Find the shortest path
  final route = Building2PathfindingService.findShortestPath(humanResources, chemLab2);
  
  if (route == null) {
    print('Error: No route found');
    return;
  }
  
  print('Route found with ${route.totalSteps} steps:');
  for (int i = 0; i < route.steps.length; i++) {
    final step = route.steps[i];
    print('${i + 1}. ${step.instruction}');
    print('   From: ${step.from.displayName} (Floor ${step.from.floor}, clage ${step.from.clage}${step.from.isReversed ? '_r' : ''})');
    print('   To: ${step.to.displayName} (Floor ${step.to.floor}, clage ${step.to.clage}${step.to.isReversed ? '_r' : ''})');
    print('');
  }
  
  // Also test the reverse route to make sure it works both ways
  print('\n--- Testing reverse route ---');
  final reverseRoute = Building2PathfindingService.findShortestPath(chemLab2, humanResources);
  
  if (reverseRoute == null) {
    print('Error: No reverse route found');
    return;
  }
  
  print('Reverse route found with ${reverseRoute.totalSteps} steps:');
  for (int i = 0; i < reverseRoute.steps.length; i++) {
    final step = reverseRoute.steps[i];
    print('${i + 1}. ${step.instruction}');
  }
}
