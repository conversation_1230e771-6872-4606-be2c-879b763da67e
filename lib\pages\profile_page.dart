import 'package:flutter/material.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storage_client/src/types.dart';
import 'package:test_1_copy_auth_events/services/auth_service.dart';
import 'package:test_1_copy_auth_events/pages/login_page.dart';
import 'package:path/path.dart' as path;
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';

class ProfilePage extends StatefulWidget {
  final VoidCallback toggleTheme;
  final bool isGuest;
  final bool isDarkMode;

  ProfilePage({
    required this.toggleTheme,
    this.isGuest = false,
    required this.isDarkMode,
  });

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage>
    with AutomaticKeepAliveClientMixin {
  // Add this to preserve state when switching tabs
  @override
  bool get wantKeepAlive => true;

  File? _image;
  bool _isLoading = true;
  String _bio = "Enter your bio here...";
  String _name = "<PERSON><PERSON><PERSON><PERSON>";
  String _address = "Pulilan, Bulacan";
  String _department = "College of Information Technology and Education";
  String _course = "BSIT";
  String _yearLevel = "3";
  bool _isEditingBio = false;
  bool _isTextPressed = false;
  String? _imageUrl;
  bool _dataLoaded = false; // Flag to track if data is already loaded
  String _tempBio = "";

  TextEditingController _bioController = TextEditingController();
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _bioController = TextEditingController(text: _bio);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataOnce();
    });
  }

  // Method to load data only once
  Future<void> _loadDataOnce() async {
    if (_dataLoaded) return; // Skip if data is already loaded

    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;
    final cacheKey = userEmail ?? 'guest';

    // First, immediately load from cache
    await _loadCachedData(prefs, cacheKey);

    // Check if we need to fetch fresh data
    final lastEmail = prefs.getString('last_profile_email');
    if (lastEmail != userEmail || !_dataLoaded) {
      // Load fresh data without showing loading indicator
      try {
        await _loadBio();
        await _fetchUserDetails();
        await _cacheProfileData();

        if (!mounted) return;

        setState(() {
          _dataLoaded = true;
        });

        await prefs.setString('last_profile_email', userEmail ?? '');
        await prefs.setBool('profile_loaded_$cacheKey', true);
      } catch (e) {
        print('Error loading profile data: $e');
      }
    }
  }

  // Method to load data from cache
  Future<void> _loadCachedData(SharedPreferences prefs, String cacheKey) async {
    setState(() {
      _name = prefs.getString('profile_name_$cacheKey') ?? _name;
      _bio = prefs.getString('profile_bio_$cacheKey') ?? _bio;
      _address = prefs.getString('profile_address_$cacheKey') ?? _address;
      _department =
          prefs.getString('profile_department_$cacheKey') ?? _department;
      _course = prefs.getString('profile_course_$cacheKey') ?? _course;
      _yearLevel = prefs.getString('profile_yearLevel_$cacheKey') ?? _yearLevel;
      _imageUrl = prefs.getString('profile_imageUrl_$cacheKey');
      _isLoading = false;

      _bioController.text = _bio;
    });

    // Load local image if needed
    final imagePath = prefs.getString('avatar');
    if (imagePath != null) {
      setState(() {
        _image = File(imagePath);
      });
    }
  }

  // Method to cache profile data
  Future<void> _cacheProfileData() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;
    final cacheKey = userEmail ?? 'guest';

    await prefs.setString('profile_name_$cacheKey', _name);
    await prefs.setString('profile_bio_$cacheKey', _bio);
    await prefs.setString('profile_address_$cacheKey', _address);
    await prefs.setString('profile_department_$cacheKey', _department);
    await prefs.setString('profile_course_$cacheKey', _course);
    await prefs.setString('profile_yearLevel_$cacheKey', _yearLevel);
    if (_imageUrl != null) {
      await prefs.setString('profile_imageUrl_$cacheKey', _imageUrl!);
    }
  }

  Future<void> _loadData() async {
    await _loadBio();
    await _fetchUserDetails(); // This will also fetch the image URL
    await _cacheProfileData(); // Cache the loaded data
    setState(() {
      _isLoading = false;
    });
  }

  // Method to force refresh profile data
  Future<void> refreshProfile() async {
    setState(() => _isLoading = true);

    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;
    final cacheKey = userEmail ?? 'guest';

    try {
      await _loadBio();
      await _fetchUserDetails();
      await _cacheProfileData();

      if (!mounted) return;

      setState(() {
        _dataLoaded = true;
        _isLoading = false;
      });

      await prefs.setString('last_profile_email', userEmail ?? '');
      await prefs.setBool('profile_loaded_$cacheKey', true);
    } catch (e) {
      print('Error refreshing profile: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<void> _fetchUserDetails() async {
    if (widget.isGuest) {
      setState(() {
        _name = "Guest User";
        _address = "Not Available";
        _department = "Not Available";
        _course = "Not Available";
        _yearLevel = "Not Available";
      });

      // For guests, try to load image from local storage
      await _loadLocalImage();
      return;
    }

    final email = _authService.userEmail;

    if (email != null) {
      final userDetails = await _authService.fetchUserDetails(email);
      if (userDetails != null) {
        setState(() {
          _name = userDetails['name'] ?? "Giancarlos De Guzman";
          _address = userDetails['address'] ?? "Pulilan, Bulacan";
          _bio = userDetails['bio'] ?? "ALREADY DEPRESSED BUT G LANG!";
          _department = userDetails['department'] ??
              "College of Information Technology and Education";
          _course = userDetails['course'] ?? "BSIT";
          _yearLevel = (userDetails['yearlvl'] ?? 3).toString();
          _bioController.text = _bio;
        });

        // Fetch image from Supabase
        await _fetchImageFromSupabase(email);
      }
    }
  }

  Future<void> _fetchImageFromSupabase(String email) async {
    try {
      // Create a transformed filename from the email that matches the uploaded format
      final fileName =
          '${email.replaceAll('@', '_at_').replaceAll('.', '_dot_')}.jpg';

      // Check if we already have the image URL in SharedPreferences and it's not expired
      final prefs = await SharedPreferences.getInstance();
      final cachedImageUrl = prefs.getString('supabase_image_url_$email');
      final cachedTimestamp =
          prefs.getInt('supabase_image_timestamp_$email') ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // If we have a cached URL and it's less than 1 hour old, use it
      // Reduce cache time to 5 minutes (300000ms) for faster updates
      if (cachedImageUrl != null && (currentTime - cachedTimestamp) < 300000) {
        setState(() {
          _imageUrl = cachedImageUrl;
        });
        return;
      }

      // Construct the image URL for fetching with a timestamp to prevent caching
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final imageUrl = _authService.client.storage
              .from('userimages')
              .getPublicUrl('uploads/$fileName') +
          '?t=$timestamp';

      // Check if the file exists in the uploads directory
      final response = await _authService.client.storage
          .from('userimages')
          .list(path: 'uploads/');

      // Check if the file exists in the uploads directory
      final fileExists = response.any((file) => file.name == fileName);

      if (fileExists) {
        // Cache the image URL with timestamp
        await prefs.setString('supabase_image_url_$email', imageUrl);
        await prefs.setInt('supabase_image_timestamp_$email', currentTime);

        setState(() {
          _imageUrl = imageUrl; // Set the image URL if the file exists
        });
      } else {
        // If the image is not found, optionally load a local fallback image
        await _loadLocalImage();
      }
    } catch (e) {
      print('Error fetching image from Supabase: $e');
      await _loadLocalImage();
    }
  }

  Future<void> _uploadImageToSupabase(String imagePath) async {
    final email = _authService.userEmail;
    if (email == null) {
      print("User is not authenticated");
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create a filename based on the email
      final fileName =
          'uploads/${email.replaceAll('@', '_at_').replaceAll('.', '_dot_')}.jpg';
      final file = File(imagePath);

      // First, attempt to delete the existing file with the same name if it exists
      try {
        await _authService.client.storage.from('userimages').remove([fileName]);
        print('Deleted existing file: $fileName');
      } catch (e) {
        print('No existing file to delete or error: $e');
      }

      // Upload the new file to Supabase storage
      final uploadResponse = await _authService.client.storage
          .from('userimages')
          .upload(fileName, file);

      if (uploadResponse.error != null) {
        print('Error uploading file: ${uploadResponse.error!.message}');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update profile image'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // Clear the cached URL to force a refresh
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('supabase_image_url_$email');

      // Immediately after uploading, get the public URL of the uploaded image with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final imageUrl = _authService.client.storage
              .from('userimages')
              .getPublicUrl(fileName) +
          '?t=$timestamp';

      // Cache the new image URL
      await prefs.setString('supabase_image_url_$email', imageUrl);
      await prefs.setInt('supabase_image_timestamp_$email',
          DateTime.now().millisecondsSinceEpoch);

      // Update the UI immediately
      setState(() {
        _imageUrl = imageUrl;
        _image = null; // Clear local image to ensure network image is used
      });

      // Optionally save the image locally for future usage
      await _saveImage(imagePath);

      // Update the profile data cache
      await _cacheProfileData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Profile image updated successfully'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error during upload process: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating profile image: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadLocalImage() async {
    final prefs = await SharedPreferences.getInstance();
    final imagePath = prefs.getString('avatar');
    if (imagePath != null) {
      setState(() {
        _image = File(imagePath);
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      // Show the image preview and confirmation dialog
      final bool shouldProceed =
          await _showImageConfirmationDialog(context, pickedFile.path);

      if (!shouldProceed) {
        return; // User canceled the operation
      }

      setState(() {
        _image = File(pickedFile.path);
        _imageUrl = null; // Clear Supabase URL to show the local image
      });

      // Show a snackbar that the image is being processed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Processing image...'),
            duration: Duration(seconds: 1),
            backgroundColor: Colors.grey,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      if (widget.isGuest) {
        // For guests, just save locally
        _saveImage(pickedFile.path);
      } else {
        // For authenticated users, upload to Supabase
        await _uploadImageToSupabase(pickedFile.path);
      }
    }
  }

  // Add this new method for image confirmation
  Future<bool> _showImageConfirmationDialog(
      BuildContext context, String imagePath) {
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Confirm Profile Image',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Do you want to use this image as your profile picture?'),
              SizedBox(height: 16),
              Container(
                height: 200,
                width: 200,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Image.file(
                    File(imagePath),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false); // Cancel
              },
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true); // Confirm
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Confirm'),
            ),
          ],
        );
      },
    ).then((value) => value ?? false); // Return false if dismissed
  }

  Future<void> _saveImage(String path) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('avatar', path);

      // For guest users, show a success message
      if (widget.isGuest && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Profile image saved locally'),
            backgroundColor: Colors.grey,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error saving image locally: $e');

      if (widget.isGuest && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save image locally'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _saveBio() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;
    final cacheKey = userEmail ?? 'guest';

    if (widget.isGuest) {
      // Save guest bio
      await prefs.setString('guestBio', _bio); // Save the guest bio
    } else {
      // Save regular user bio
      prefs.setString('bio', _bio); // Save the bio to local storage

      // Update bio in Supabase only for registered users
      if (userEmail != null) {
        await _authService.updateBio(userEmail, _bio); // Update bio in Supabase
      }
    }

    // Update the profile data cache
    await prefs.setString('profile_bio_$cacheKey', _bio);
  }

  Future<void> _loadBio() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;
    final cacheKey = userEmail ?? 'guest';

    // First try to get from cache
    final cachedBio = prefs.getString('profile_bio_$cacheKey');
    if (cachedBio != null) {
      setState(() {
        _bio = cachedBio;
        _bioController.text = cachedBio;
      });
      return; // Exit early if we have cached bio
    }

    // If no cache, set default and then fetch
    setState(() {
      _bio = widget.isGuest ? "Guest Bio" : "Loading...";
      _bioController.text = _bio;
    });

    if (!widget.isGuest && userEmail != null) {
      final userDetails = await _authService.fetchUserDetails(userEmail);
      if (userDetails != null && userDetails['bio'] != null) {
        final newBio = userDetails['bio'];
        setState(() {
          _bio = newBio;
          _bioController.text = newBio;
        });
        // Cache the fetched bio
        await prefs.setString('profile_bio_$cacheKey', newBio);
      }
    }
  }

  Future<bool> _showLogoutConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Confirm Logout',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text('Are you sure you want to log out?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false); // Cancel
              },
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true); // Confirm
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Logout'),
            ),
          ],
        );
      },
    ).then((value) => value ?? false); // Return false if dismissed
  }

// Update your existing _logout method to show loading
  Future<void> _logout(BuildContext context) async {
    setState(() {
      _isLoading = true; // Start loading
    });

    // Sign out the user
    await _authService.signOut();

    // Clear guest state and any saved user credentials
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isGuest', false); // Clear guest state
    await prefs.remove('userEmail'); // Remove saved email
    await prefs.remove('password'); // Remove saved password if you have it

    // Clear all cached data
    final email = _authService.userEmail;
    final cacheKey = email ?? 'guest';
    await prefs.remove('profile_loaded_$cacheKey');
    await prefs.remove('tasks_loaded_${widget.isGuest ? 'guest' : email}');
    await prefs.remove('tasks_loaded_${_authService.userEmail ?? 'guest'}');
    await prefs.remove('cached_tasks_${_authService.userEmail ?? 'guest'}');
    await prefs.remove('tasks_${_authService.userEmail ?? 'guest'}');
    await prefs.remove('profile_loaded_${_authService.userEmail ?? 'guest'}');

    // Clear other user-related data
    await prefs.remove('bio');
    await prefs.remove('avatar');

    // Navigate to LoginPage
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => LoginPage(toggleTheme: widget.toggleTheme),
      ),
    );

    setState(() {
      _isLoading = false; // Stop loading
    });
  }

  void _showImageSourceActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                'Choose an image source',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  ElevatedButton.icon(
                    icon: Icon(
                      Icons.camera_alt,
                      color: Colors.green,
                    ),
                    label: Text('Camera',
                        style: TextStyle(
                            color: widget.isDarkMode
                                ? Colors.white
                                : Colors.black)),
                    onPressed: () {
                      Navigator.of(context).pop();
                      _pickImage(ImageSource.camera);
                    },
                  ),
                  ElevatedButton.icon(
                    icon: Icon(
                      Icons.photo_library,
                      color: Colors.green,
                    ),
                    label: Text('Gallery',
                        style: TextStyle(
                            color: widget.isDarkMode
                                ? Colors.white
                                : Colors.black)),
                    onPressed: () {
                      Navigator.of(context).pop();
                      _pickImage(ImageSource.gallery);
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _startEditingBio() {
    setState(() {
      _isEditingBio = true;
      _tempBio = _bio; // Save current bio to restore if canceled
      _bioController.text = _bio;
    });
  }

  void _saveBioChanges() {
    setState(() {
      _bio = _bioController.text;
      _isEditingBio = false;
    });
    _saveBio();
  }

  void _cancelBioEditing() {
    setState(() {
      _isEditingBio = false;
      _bioController.text = _tempBio; // Restore original bio
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Add this line for AutomaticKeepAliveClientMixin
    final isDark = widget.isDarkMode;

    // Color scheme based on theme
    final Color textColor = isDark ? Colors.white : Colors.black87;
    final Color secondaryTextColor =
        isDark ? Colors.white70 : Colors.grey[700]!;
    final Color bioBackgroundColor =
        isDark ? Colors.grey[850]! : Colors.grey[200]!;
    final Color bioTextColor = isDark ? Colors.white : Colors.black87;
    final Color fieldBorderColor =
        isDark ? Colors.grey[600]! : Colors.grey[400]!;

    return Scaffold(
      appBar: AppBar(
        title: Text('Profile',
            style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold)),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(20),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.help_outline, color: Colors.white),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'Profile Support',
          ),
          IconButton(
            icon: Icon(Icons.brightness_6, color: Colors.white),
            onPressed: widget.toggleTheme,
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              ),
            )
          : RefreshIndicator(
              color: Colors.green,
              onRefresh:
                  refreshProfile, // Call refreshProfile on pull-to-refresh
              child: SingleChildScrollView(
                physics:
                    AlwaysScrollableScrollPhysics(), // Important for RefreshIndicator to work
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: 5),
                    // Profile Image
                    Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        Container(
                          width: 125,
                          height: 125,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.green,
                              width: 3,
                            ),
                          ),
                          child: GestureDetector(
                            onTap: () {
                              if (_imageUrl != null || _image != null) {
                                showDialog(
                                  context: context,
                                  builder: (context) => Dialog(
                                    backgroundColor: Colors.transparent,
                                    child: GestureDetector(
                                      onTap: () => Navigator.of(context).pop(),
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          color: Colors.white,
                                        ),
                                        padding: EdgeInsets.all(3),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: _imageUrl != null
                                              ? Image.network(_imageUrl!,
                                                  fit: BoxFit.contain)
                                              : _image != null
                                                  ? Image.file(_image!,
                                                      fit: BoxFit.contain)
                                                  : SizedBox.shrink(),
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }
                            },
                            child: CircleAvatar(
                              backgroundColor: Colors.grey[300],
                              backgroundImage: _imageUrl != null
                                  ? NetworkImage(_imageUrl!)
                                  : _image != null
                                      ? FileImage(_image!) as ImageProvider
                                      : AssetImage('assets/BUtility.jpg'),
                              child: _imageUrl == null && _image == null
                                  ? null
                                  : null,
                            ),
                          ),
                        ),
                        CircleAvatar(
                          backgroundColor: Colors.green,
                          radius: 17.5,
                          child: IconButton(
                            icon: Icon(Icons.camera_alt,
                                color: Colors.white, size: 20),
                            onPressed: () =>
                                _showImageSourceActionSheet(context),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 16),
                    Text(
                      _name,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),

                    // Bio Section
                    SizedBox(height: 15),
                    _buildBioSection(isDark, bioBackgroundColor, bioTextColor,
                        fieldBorderColor),

                    SizedBox(height: 24),

                    // Profile Information
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          _buildProfileItemWithIcon(
                              Icons.location_on,
                              "Address",
                              _address,
                              textColor,
                              secondaryTextColor),
                          SizedBox(height: 16),
                          _buildProfileItemWithIcon(
                              Icons.business,
                              "Department",
                              _department,
                              textColor,
                              secondaryTextColor),
                          SizedBox(height: 16),
                          _buildProfileItemWithIcon(
                            Icons.school,
                            (_department
                                        .toUpperCase()
                                        .contains('SENIOR HIGH') ||
                                    _department
                                        .toUpperCase()
                                        .contains('JUNIOR HIGH'))
                                ? "Strand"
                                : "Course",
                            _course,
                            textColor,
                            secondaryTextColor,
                          ),
                          SizedBox(height: 16),
                          _buildProfileItemWithIcon(Icons.stars, "Year Level",
                              _yearLevel, textColor, secondaryTextColor),
                        ],
                      ),
                    ),

                    SizedBox(height: 25),

                    // Logout button
                    GestureDetector(
                      onTap: () async {
                        // Show confirmation dialog
                        final shouldLogout =
                            await _showLogoutConfirmationDialog(context);
                        if (shouldLogout) {
                          _logout(context);
                        }
                      },
                      onLongPressEnd: (_) {
                        setState(() {
                          _isTextPressed = false;
                        });
                      },
                      child: Text(
                        "Logout",
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          decoration:
                              _isTextPressed ? TextDecoration.underline : null,
                        ),
                      ),
                    ),
                    SizedBox(height: 0),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBioSection(bool isDark, Color bioBackgroundColor,
      Color bioTextColor, Color fieldBorderColor) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Bio',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              _isEditingBio
                  ? Row(
                      children: [
                        // Cancel button
                        IconButton(
                          icon: Icon(
                            Icons.close,
                            color: Colors.red,
                          ),
                          onPressed: _cancelBioEditing,
                        ),
                        // Save button
                        IconButton(
                          icon: Icon(
                            Icons.check,
                            color: Colors.green,
                          ),
                          onPressed: _saveBioChanges,
                        ),
                      ],
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.edit,
                        color: Colors.green,
                      ),
                      onPressed: _startEditingBio,
                    ),
            ],
          ),
          _isEditingBio
              ? TextField(
                  controller: _bioController,
                  maxLines: 3,
                  style: TextStyle(color: bioTextColor),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: fieldBorderColor),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: fieldBorderColor),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.green),
                    ),
                    hintText: 'Enter your bio here...',
                    hintStyle: TextStyle(
                        color: isDark ? Colors.grey[400] : Colors.grey[600]),
                    filled: true,
                    fillColor: bioBackgroundColor,
                  ),
                )
              : Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: bioBackgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: fieldBorderColor),
                  ),
                  child: Text(
                    _bio,
                    style: TextStyle(fontSize: 16, color: bioTextColor),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildProfileItemWithIcon(IconData icon, String label, String value,
      Color textColor, Color secondaryTextColor) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 25,
          color: Colors.green,
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 17.5,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
              SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  color: textColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _bioController.dispose();
    super.dispose();
  }
}

extension on String {
  get error => null;
}

extension on List<FileObject> {
  get error => null;
}

Future<void> _openSupportEmail(BuildContext context) async {
  final Uri emailLaunchUri = Uri(
    scheme: 'mailto',
    path: '<EMAIL>',
    query: encodeQueryParameters({
      'subject': 'BUtility Profile Support',
      'body': '''
User Information:
- Email: ${AuthService().userEmail}

Issue Description:
[Please describe your issue here]

Thank you,
[Your Name]
'''
    }),
  );

  try {
    await launchUrl(emailLaunchUri);
  } catch (e) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Could not open email app'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }
}

// Helper method to encode query parameters
String? encodeQueryParameters(Map<String, String> params) {
  return params.entries
      .map((e) =>
          '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
      .join('&');
}

void _showHelpDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(
          'Profile Help',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'If you\'re experiencing issues with your profile information:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              '• Profile image not updating? Try using a different image.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '• Bio not saving? Make sure you tap the check mark to save changes.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 8),
            Text(
              '• Personal details incorrect? Contact support for assistance.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 3),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Text(
                    '<EMAIL>',
                    style: TextStyle(fontSize: 14, color: Colors.blue),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8), // Small space between text and icon
                IconButton(
                  icon: Icon(Icons.copy, size: 18),
                  tooltip: 'Copy email',
                  onPressed: () {
                    Clipboard.setData(
                        ClipboardData(text: '<EMAIL>'));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Email copied to clipboard')),
                    );
                  },
                ),
              ],
            ),
            SizedBox(height: 5),
            InkWell(
              onTap: () => _openSupportEmail(context),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.email, color: Colors.green),
                  SizedBox(width: 8),
                  Text(
                    'Contact Support',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text('Close', style: TextStyle(color: Colors.red)),
          ),
        ],
      );
    },
  );
}
