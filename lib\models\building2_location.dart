class Building2Location {
  final String id;
  final String name;
  final String displayName;
  final int floor;
  final int clage;
  final bool isReversed;
  final List<String> keywords;
  final List<String> aliases;

  const Building2Location({
    required this.id,
    required this.name,
    required this.displayName,
    required this.floor,
    required this.clage,
    required this.isReversed,
    this.keywords = const [],
    this.aliases = const [],
  });

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Building2Location &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class Building2LocationData {
  static const List<Building2Location> allLocations = [
    // Floor 1 (clage 0-10)
    Building2Location(
      id: 'f1_c0',
      name: 'Main Entrance',
      displayName: 'Main Entrance',
      floor: 1,
      clage: 0,
      isReversed: false,
      keywords: ['entrance', 'main', 'lobby', 'stairs'],
      aliases: ['lobby', 'main lobby', 'entrance hall'],
    ),
    Building2Location(
      id: 'f1_c1',
      name: 'CAS 101',
      displayName: 'CAS 101',
      floor: 1,
      clage: 1,
      isReversed: false,
      keywords: ['101', 'classroom', 'room', 'cas'],
      aliases: ['room 101', 'classroom 101'],
    ),
    Building2Location(
      id: 'f1_c2',
      name: 'Hydraulics Laboratory',
      displayName: 'Hydraulics Laboratory',
      floor: 1,
      clage: 2,
      isReversed: false,
      keywords: ['hydraulics', 'laboratory', 'lab', 'engineering'],
      aliases: ['hydraulics lab', 'hydro lab'],
    ),
    Building2Location(
      id: 'f1_c3',
      name: 'CAS 103',
      displayName: 'CAS 103',
      floor: 1,
      clage: 3,
      isReversed: false,
      keywords: ['103', 'classroom', 'room', 'cas'],
      aliases: ['room 103', 'classroom 103'],
    ),
    Building2Location(
      id: 'f1_c4',
      name: 'Office of the Dean - CEDE',
      displayName: 'Office of the Dean - CEDE',
      floor: 1,
      clage: 4,
      isReversed: false,
      keywords: ['dean', 'office', 'cede', 'administration'],
      aliases: ['dean office', 'cede dean', 'dean cede'],
    ),
    Building2Location(
      id: 'f1_c5',
      name: 'EE Laboratory',
      displayName: 'EE Laboratory',
      floor: 1,
      clage: 5,
      isReversed: false,
      keywords: ['ee', 'electrical', 'engineering', 'laboratory', 'lab'],
      aliases: ['electrical lab', 'ee lab', 'electrical engineering lab'],
    ),
    Building2Location(
      id: 'f1_c6',
      name: 'Office of the Vice President for Academic Affairs',
      displayName: 'Office of the Vice President for Academic Affairs',
      floor: 1,
      clage: 6,
      isReversed: false,
      keywords: ['vice', 'president', 'academic', 'affairs', 'office', 'vpaa'],
      aliases: ['vpaa office', 'vice president office', 'academic affairs'],
    ),
    Building2Location(
      id: 'f1_c7',
      name: 'International Student Lounge',
      displayName: 'International Student Lounge',
      floor: 1,
      clage: 7,
      isReversed: false,
      keywords: ['international', 'student', 'lounge', 'foreign'],
      aliases: ['student lounge', 'international lounge'],
    ),
    Building2Location(
      id: 'f1_c8',
      name: 'Office of the Dean School of Graduate Studies',
      displayName: 'Office of the Dean School of Graduate Studies',
      floor: 1,
      clage: 8,
      isReversed: false,
      keywords: ['dean', 'graduate', 'studies', 'office', 'school'],
      aliases: ['graduate dean', 'graduate studies', 'sgs dean'],
    ),
    Building2Location(
      id: 'f1_c9',
      name: 'CAS 106',
      displayName: 'CAS 106',
      floor: 1,
      clage: 9,
      isReversed: false,
      keywords: ['106', 'classroom', 'room', 'cas'],
      aliases: ['room 106', 'classroom 106'],
    ),
    Building2Location(
      id: 'f1_c10',
      name: 'College of Law',
      displayName: 'College of Law',
      floor: 1,
      clage: 10,
      isReversed: false,
      keywords: ['law', 'college', 'legal', 'attorney'],
      aliases: ['law college', 'law school'],
    ),

    // Floor 2 (clage 1-10, no clage 0)
    Building2Location(
      id: 'f2_c1',
      name: 'Human Resources Department',
      displayName: 'Human Resources Department',
      floor: 2,
      clage: 1,
      isReversed: false,
      keywords: ['human', 'resources', 'hr', 'department', 'personnel'],
      aliases: ['hr department', 'hr office', 'human resources'],
    ),
    Building2Location(
      id: 'f2_c2',
      name: 'CAS 202',
      displayName: 'CAS 202',
      floor: 2,
      clage: 2,
      isReversed: false,
      keywords: ['202', 'classroom', 'room', 'cas'],
      aliases: ['room 202', 'classroom 202'],
    ),
    Building2Location(
      id: 'f2_c3',
      name: 'CEDE Faculty Room',
      displayName: 'CEDE Faculty Room',
      floor: 2,
      clage: 3,
      isReversed: false,
      keywords: ['cede', 'faculty', 'room', 'teachers'],
      aliases: ['faculty room', 'cede faculty'],
    ),
    Building2Location(
      id: 'f2_c4',
      name: 'Drawing Room',
      displayName: 'Drawing Room',
      floor: 2,
      clage: 4,
      isReversed: false,
      keywords: ['drawing', 'room', 'drafting', 'design'],
      aliases: ['drafting room', 'design room'],
    ),
    Building2Location(
      id: 'f2_c5',
      name: 'Conference Room',
      displayName: 'Conference Room',
      floor: 2,
      clage: 5,
      isReversed: false,
      keywords: ['conference', 'room', 'meeting', 'boardroom'],
      aliases: ['meeting room', 'boardroom'],
    ),
    Building2Location(
      id: 'f2_c6',
      name: 'Psychology Laboratory',
      displayName: 'Psychology Laboratory',
      floor: 2,
      clage: 6,
      isReversed: false,
      keywords: ['psychology', 'laboratory', 'lab', 'psych'],
      aliases: ['psych lab', 'psychology lab'],
    ),
    Building2Location(
      id: 'f2_c7',
      name: 'Office of the Dean - CLAGE',
      displayName: 'Office of the Dean - CLAGE',
      floor: 2,
      clage: 7,
      isReversed: false,
      keywords: ['dean', 'office', 'clage', 'administration'],
      aliases: ['dean office', 'clage dean', 'dean clage'],
    ),
    Building2Location(
      id: 'f2_c8',
      name: 'CAS 205',
      displayName: 'CAS 205',
      floor: 2,
      clage: 8,
      isReversed: false,
      keywords: ['205', 'classroom', 'room', 'cas'],
      aliases: ['room 205', 'classroom 205'],
    ),
    Building2Location(
      id: 'f2_c9',
      name: 'CAS 206',
      displayName: 'CAS 206',
      floor: 2,
      clage: 9,
      isReversed: false,
      keywords: ['206', 'classroom', 'room', 'cas'],
      aliases: ['room 206', 'classroom 206'],
    ),
    Building2Location(
      id: 'f2_c1_r',
      name: 'Second Floor Staircase (Reversed)',
      displayName: 'Second Floor Staircase (Reversed)',
      floor: 2,
      clage: 1,
      isReversed: true,
      keywords: ['second', 'floor', 'stairs', 'staircase', 'reversed'],
      aliases: ['2nd floor stairs reversed', 'second floor staircase reversed'],
    ),
    Building2Location(
      id: 'f2_c10',
      name: 'Second Floor Staircase',
      displayName: 'Second Floor Staircase',
      floor: 2,
      clage: 10,
      isReversed: false,
      keywords: ['second', 'floor', 'stairs', 'staircase'],
      aliases: ['2nd floor stairs', 'second floor staircase'],
    ),

    // Floor 3 (clage 1-9)
    Building2Location(
      id: 'f3_c1',
      name: 'Chemistry Laboratory 1',
      displayName: 'Chemistry Laboratory 1',
      floor: 3,
      clage: 1,
      isReversed: false,
      keywords: ['chemistry', 'laboratory', 'lab', 'chem', '1'],
      aliases: ['chem lab 1', 'chemistry lab 1'],
    ),
    Building2Location(
      id: 'f3_c2',
      name: 'Chemistry Laboratory 2',
      displayName: 'Chemistry Laboratory 2',
      floor: 3,
      clage: 2,
      isReversed: false,
      keywords: ['chemistry', 'laboratory', 'lab', 'chem', '2'],
      aliases: ['chem lab 2', 'chemistry lab 2'],
    ),
    Building2Location(
      id: 'f3_c3',
      name: 'Center for Science Laboratories and Services',
      displayName: 'Center for Science Laboratories and Services',
      floor: 3,
      clage: 3,
      isReversed: false,
      keywords: ['science', 'laboratories', 'services', 'center'],
      aliases: ['science center', 'lab services'],
    ),
    Building2Location(
      id: 'f3_c4',
      name: 'Supply Room',
      displayName: 'Supply Room',
      floor: 3,
      clage: 4,
      isReversed: false,
      keywords: ['supply', 'room', 'storage', 'materials'],
      aliases: ['storage room', 'supplies'],
    ),
    Building2Location(
      id: 'f3_c5',
      name: 'CAS 305',
      displayName: 'CAS 305',
      floor: 3,
      clage: 5,
      isReversed: false,
      keywords: ['305', 'classroom', 'room', 'cas'],
      aliases: ['room 305', 'classroom 305'],
    ),
    Building2Location(
      id: 'f3_c6',
      name: 'Medtech Laboratory',
      displayName: 'Medtech Laboratory',
      floor: 3,
      clage: 6,
      isReversed: false,
      keywords: ['medtech', 'medical', 'technology', 'laboratory', 'lab'],
      aliases: ['medtech lab', 'medical technology lab'],
    ),
    Building2Location(
      id: 'f3_c7',
      name: 'CAS 306',
      displayName: 'CAS 306',
      floor: 3,
      clage: 7,
      isReversed: false,
      keywords: ['306', 'classroom', 'room', 'cas'],
      aliases: ['room 306', 'classroom 306'],
    ),
    Building2Location(
      id: 'f3_c8',
      name: 'CAS 307',
      displayName: 'CAS 307',
      floor: 3,
      clage: 8,
      isReversed: false,
      keywords: ['307', 'classroom', 'room', 'cas'],
      aliases: ['room 307', 'classroom 307'],
    ),
    Building2Location(
      id: 'f3_c1_r',
      name: 'Third Floor Staircase (Reversed)',
      displayName: 'Third Floor Staircase (Reversed)',
      floor: 3,
      clage: 1,
      isReversed: true,
      keywords: ['third', 'floor', 'stairs', 'staircase', 'reversed'],
      aliases: ['3rd floor stairs reversed', 'third floor staircase reversed'],
    ),
    Building2Location(
      id: 'f3_c9',
      name: 'Third Floor Staircase',
      displayName: 'Third Floor Staircase',
      floor: 3,
      clage: 9,
      isReversed: false,
      keywords: ['third', 'floor', 'stairs', 'staircase'],
      aliases: ['3rd floor stairs', 'third floor staircase'],
    ),

    // Floor 4 (clage 0-10)
    Building2Location(
      id: 'f4_c0',
      name: 'Fourth Floor Staircase',
      displayName: 'Fourth Floor Staircase',
      floor: 4,
      clage: 0,
      isReversed: false,
      keywords: ['fourth', 'floor', 'stairs', 'staircase'],
      aliases: ['4th floor stairs', 'fourth floor entrance'],
    ),
    Building2Location(
      id: 'f4_c1',
      name: 'CAS 401',
      displayName: 'CAS 401',
      floor: 4,
      clage: 1,
      isReversed: false,
      keywords: ['401', 'classroom', 'room', 'cas'],
      aliases: ['room 401', 'classroom 401'],
    ),
    Building2Location(
      id: 'f4_c2',
      name: 'CAS 402',
      displayName: 'CAS 402',
      floor: 4,
      clage: 2,
      isReversed: false,
      keywords: ['402', 'classroom', 'room', 'cas'],
      aliases: ['room 402', 'classroom 402'],
    ),
    Building2Location(
      id: 'f4_c3',
      name: 'CAS 403',
      displayName: 'CAS 403',
      floor: 4,
      clage: 3,
      isReversed: false,
      keywords: ['403', 'classroom', 'room', 'cas'],
      aliases: ['room 403', 'classroom 403'],
    ),
    Building2Location(
      id: 'f4_c4',
      name: 'Office of the Dean - CBAA',
      displayName: 'Office of the Dean - CBAA',
      floor: 4,
      clage: 4,
      isReversed: false,
      keywords: ['dean', 'office', 'cbaa', 'administration', 'business'],
      aliases: ['dean office', 'cbaa dean', 'dean cbaa', 'business dean'],
    ),
    Building2Location(
      id: 'f4_c5',
      name: 'CBAA Faculty Room',
      displayName: 'CBAA Faculty Room',
      floor: 4,
      clage: 5,
      isReversed: false,
      keywords: ['cbaa', 'faculty', 'room', 'teachers', 'business'],
      aliases: ['faculty room', 'cbaa faculty', 'business faculty'],
    ),
    Building2Location(
      id: 'f4_c6',
      name: 'Consultation Room 1',
      displayName: 'Consultation Room 1',
      floor: 4,
      clage: 6,
      isReversed: false,
      keywords: ['consultation', 'room', 'meeting', 'counseling', '1'],
      aliases: ['consult room 1', 'counseling room 1'],
    ),
    Building2Location(
      id: 'f4_c7',
      name: 'Consultation Room 2',
      displayName: 'Consultation Room 2',
      floor: 4,
      clage: 7,
      isReversed: false,
      keywords: ['consultation', 'room', 'meeting', 'counseling', '2'],
      aliases: ['consult room 2', 'counseling room 2'],
    ),
    Building2Location(
      id: 'f4_c8',
      name: 'CAS 404',
      displayName: 'CAS 404',
      floor: 4,
      clage: 8,
      isReversed: false,
      keywords: ['404', 'classroom', 'room', 'cas'],
      aliases: ['room 404', 'classroom 404'],
    ),
    Building2Location(
      id: 'f4_c1_r',
      name: 'Fourth Floor Staircase (Reversed)',
      displayName: 'Fourth Floor Staircase (Reversed)',
      floor: 4,
      clage: 1,
      isReversed: true,
      keywords: ['fourth', 'floor', 'stairs', 'staircase', 'reversed'],
      aliases: ['4th floor stairs reversed', 'fourth floor staircase reversed'],
    ),
    Building2Location(
      id: 'f4_c9',
      name: 'Fourth Floor Staircase',
      displayName: 'Fourth Floor Staircase',
      floor: 4,
      clage: 9,
      isReversed: false,
      keywords: ['fourth', 'floor', 'stairs', 'staircase'],
      aliases: ['4th floor stairs', 'fourth floor staircase'],
    ),
    Building2Location(
      id: 'f4_c10',
      name: 'CAS 406',
      displayName: 'CAS 406',
      floor: 4,
      clage: 10,
      isReversed: false,
      keywords: ['406', 'classroom', 'room', 'cas'],
      aliases: ['room 406', 'classroom 406'],
    ),
  ];

  static List<Building2Location> searchLocations(String query) {
    if (query.isEmpty) return allLocations;

    final lowerQuery = query.toLowerCase();
    return allLocations.where((location) {
      return location.displayName.toLowerCase().contains(lowerQuery) ||
          location.name.toLowerCase().contains(lowerQuery) ||
          location.keywords
              .any((keyword) => keyword.toLowerCase().contains(lowerQuery)) ||
          location.aliases
              .any((alias) => alias.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  static Building2Location? findLocationById(String id) {
    try {
      return allLocations.firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }

  static Building2Location? findLocationByPosition(int floor, int clage,
      {bool isReversed = false}) {
    try {
      return allLocations.firstWhere(
        (location) =>
            location.floor == floor &&
            location.clage == clage &&
            location.isReversed == isReversed,
      );
    } catch (e) {
      return null;
    }
  }

  static int getMaxClageForFloor(int floor) {
    switch (floor) {
      case 1:
      case 2:
      case 4:
        return 10; // 1st, 2nd, and 4th floors go up to clage10
      case 3:
        return 9; // 3rd floor goes up to clage9
      default:
        return 10;
    }
  }
}
