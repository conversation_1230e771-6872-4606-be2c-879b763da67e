import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/image_cache_service.dart';

class Building1Navigation extends StatefulWidget {
  final bool isGuest;
  final VoidCallback onExit;
  final bool shouldResetState;
  final Map<String, dynamic>? initialState;

  Building1Navigation({
    required this.isGuest,
    required this.onExit,
    this.shouldResetState = false,
    this.initialState,
  });

  @override
  _Building1NavigationState createState() => _Building1NavigationState();
}

class _Building1NavigationState extends State<Building1Navigation> {
  int _currentMain = 0;
  bool _isReversed = false;
  bool _isLoading = true;
  String? _currentMapImage;
  final ImageCacheService _imageCacheService = ImageCacheService();
  BuildContext? _safeContext;
  int _currentFloor = 1; // Add this property if not already defined
  int _previousLocationBeforeMain16 = 15; // Default to 15

  @override
  void initState() {
    super.initState();
    _initializeState();

    // Preload images using the cache service after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Check if widget is still mounted
        _imageCacheService.preloadBuildingImages(context, 1);
      }
    });
  }

  Future<void> _initializeState() async {
    if (widget.shouldResetState) {
      setState(() {
        _currentMain = 0;
        _isReversed = false;
        _currentMapImage = _getMapImage();
        _isLoading = false;
      });
      return;
    }

    await _loadNavigationState();
  }

  Future<void> _loadNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    final loadedMain = prefs.getInt('${stateKey}_building1_current_main') ?? 0;
    final loadedReversed =
        prefs.getBool('${stateKey}_building1_is_reversed') ?? false;

    setState(() {
      _currentMain = loadedMain;
      _isReversed = loadedReversed;
      _currentMapImage = _getMapImage();
      _isLoading = false;
    });
  }

  Future<void> _saveNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.setInt('${stateKey}_building1_current_main', _currentMain);
    await prefs.setBool('${stateKey}_building1_is_reversed', _isReversed);
  }

  // Add a method to clear navigation state
  Future<void> _clearNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.remove('${stateKey}_building1_current_main');
    await prefs.remove('${stateKey}_building1_is_reversed');
  }

  void _toggleReverse() {
    setState(() {
      // Don't toggle reverse if we're at main16
      if (_currentMain == 16) {
        return;
      }

      _isReversed = !_isReversed;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  String _getLocationName() {
    // First check which floor we're on
    if (_currentFloor == 2) {
      switch (_currentMain) {
        case 1:
          return 'IMCI Laboratory';
        case 2:
          return 'Community Health Nursing Laboratory';
        case 3:
          return 'Medical Surgical Laboratory';
        case 4:
          return 'Nursing Arts Laboratory 3';
        case 5:
          return 'Faculty Lounge';
        case 6:
          return 'Center for Community Services - HALINA';
        case 7:
          return 'Office of the Director Center for Student Life and Activities';
        case 8:
          return 'Institutional Planning and Development';
        case 9:
          return 'Labor Room/Nursery Room';
        default:
          return 'Unknown Location';
      }
    } else if (_currentFloor == 3) {
      switch (_currentMain) {
        case 0:
          return 'CNAHS - 3rd Floor';
        case 1:
          return 'Intensive care Unit';
        default:
          return 'Unknown Location';
      }
    } else {
      // Original 1st floor locations
      switch (_currentMain) {
        case 0:
          return 'Accounting/Admissions Office';
        case 1:
          return 'Finance Office';
        case 2:
          return 'Finance Office';
        case 3:
          return 'Canteen';
        case 4:
          return 'Canteen';
        case 5:
          return 'Canteen';
        case 6:
          return 'Canteen - Hallway';
        case 7:
          return 'Canteen - Hallway';
        case 8:
          return 'Canteen - Hallway';
        case 9:
          return 'Book Store';
        case 10:
          return 'Book Store';
        case 11:
          return 'Book Store';
        case 12:
          return 'Office the Dean - CNAHS';
        case 13:
          return 'Faculty Room Nursing Program';
        case 14:
          return 'Demonstration Room';
        case 15:
          return 'Demonstration Room';
        case 16:
          return 'Registrar Office';
        case 17:
          return 'Registrar Office';
        case 18:
          return 'Registrar Office';
        case 19:
          return 'BC Alumni Office - Hallway';
        case 20:
          return 'BC Alumni Office - Hallway';
        case 21:
          return 'BC Alumni Office';
        case 22:
          return 'BC Alumni Office';
        case 23:
          return 'Bahay na Bato';
        case 24:
          return 'Library - Hallway';
        case 25:
          return 'Library';
        case 26:
          return 'Main Conference Room';
        default:
          return 'Unknown Location';
      }
    }
  }

  String _getMapImage() {
    // Make sure we don't try to access non-existent images
    if (_currentMain > 26) {
      _currentMain = 26; // Cap at 26 which is the highest available image
    }

    if (_currentMain == 0) {
      if (_currentFloor == 1) {
        return 'assets/MAIN/main0.jpg';
      } else if (_currentFloor == 3) {
        return 'assets/MAIN/3main0.jpg';
      }
    }

    // Special case for main16 - never use reversed version
    if (_currentMain == 16) {
      return 'assets/MAIN/main16.jpg';
    }

    // Special case for main26
    if (_currentMain == 26) {
      return 'assets/MAIN/main26${_isReversed ? '_r' : ''}.jpg';
    }

    // Handle floor-specific images
    if (_currentFloor == 2) {
      return 'assets/MAIN/2main${_currentMain}${_isReversed ? '_r' : ''}.jpg';
    } else if (_currentFloor == 3) {
      return 'assets/MAIN/3main${_currentMain}${_isReversed ? '_r' : ''}.jpg';
    } else if (_currentFloor == 4) {
      return 'assets/MAIN/4main${_currentMain}${_isReversed ? '_r' : ''}.jpg';
    }

    return 'assets/MAIN/main${_currentMain}${_isReversed ? '_r' : ''}.jpg';
  }

  void _moveForward() {
    // Special case: at 2main9, show building change dialog
    if (_currentFloor == 2 && _currentMain == 9 && !_isReversed) {
      _showChangeBuildingDialog();
      return; // Exit early to prevent state changes
    }
    // Special case: at 3main1, show building info dialog
    if (_currentFloor == 3 && _currentMain == 1 && !_isReversed) {
      _showChangeBuildingDialog1();
      return; // Exit early to prevent state changes
    }
    // Special case: at 2main9_r, go to 2main8
    if (_currentFloor == 2 && _currentMain == 9 && _isReversed) {
      setState(() {
        _currentMain = 8;
        _isReversed = false;
        _currentMapImage = _getMapImage();
      });
      _saveNavigationState();
      return;
    }
    // Special case: at main16 (Registrar), forward should go to main17 (normal mode)
    if (_currentFloor == 1 && _currentMain == 16) {
      setState(() {
        _currentMain = 17;
        _isReversed = false;
        _currentMapImage = _getMapImage();
      });
      _saveNavigationState();
      return;
    }

    setState(() {
      if (_isReversed) {
        // In reversed mode
        if ((_currentMain > 1 &&
                _currentMain != 5 &&
                _currentMain != 9 &&
                _currentMain != 12) ||
            (_currentFloor == 2 && _currentMain == 5) ||
            (_currentFloor == 2 && _currentMain == 9)) {
          // Enable forward at 2main9_r
          _currentMain--;
        } else if (_currentMain == 1) {
          _isReversed = false;
          _currentMain = 0;
        }
      } else {
        // In normal mode
        if ((_currentMain < 25 &&
                _currentMain != 4 &&
                _currentMain != 8 &&
                _currentMain != 11 &&
                _currentMain != 15) ||
            (_currentFloor == 2 && _currentMain == 4) ||
            (_currentFloor == 2 && _currentMain == 8)) {
          _currentMain++;
        } else if (_currentMain == 15) {
          // Special case: from main15, move forward to main16
          _currentMain = 16;
        }
      }
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveBackward() {
    setState(() {
      // Special case: if at main26_r, go down to main1_r
      if (_currentMain == 26 && _isReversed) {
        _currentMain = 1;
        _isReversed = true;
        _currentMapImage = _getMapImage();
        _saveNavigationState();
        return;
      }

      if (_isReversed) {
        // In reversed mode
        if ((_currentMain < 25 &&
                _currentMain != 4 &&
                _currentMain != 8 &&
                _currentMain != 11 &&
                _currentMain != 15 &&
                !(_currentFloor == 2 &&
                    _currentMain == 9)) || // Disable backward at 2main9_r
            (_currentFloor == 2 && _currentMain == 4)) {
          _currentMain++;
        }
      } else {
        // In normal mode
        if ((_currentMain > 1 &&
                _currentMain != 5 &&
                _currentMain != 9 &&
                _currentMain != 12) ||
            (_currentFloor == 2 && _currentMain == 5)) {
          _currentMain--;
        } else if (_currentMain == 1) {
          _currentMain = 0;
        }
      }

      // Special case: if we end up at main16, ensure we're not in reversed mode
      if (_currentMain == 16) {
        _isReversed = false;
      }

      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _turnLeft() {
    setState(() {
      if (_isReversed) {
        // If at main5_r, show confirmation dialog
        if (_currentMain == 5 && _currentFloor == 1) {
          _turnLeftFromMain5Reversed();
          return; // Exit early to prevent state changes before dialog selection
        }
        // If at main15_r, show confirmation dialog
        else if (_currentMain == 15) {
          _turnLeftFromMain15Reversed();
          return; // Exit early to prevent state changes before dialog selection
        }
        // If at main9_r, turn left to go to main8_r
        else if (_currentMain == 9) {
          _currentMain = 8;
        }
        // If at main12_r, turn left to go to main11_r
        else if (_currentMain == 12) {
          _currentMain = 11;
        }
      } else {
        // If at main4, turn left to main5
        if (_currentMain == 4) {
          _currentMain = 5;
        }
      }
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _turnRight() {
    setState(() {
      if (!_isReversed) {
        // If at main4, show confirmation dialog
        if (_currentMain == 4) {
          _turnRightFromMain4();
          return; // Exit early to prevent state changes before dialog selection
        }
        // If at main15, show confirmation dialog
        else if (_currentMain == 15) {
          _turnRightFromMain15();
          return; // Exit early to prevent state changes before dialog selection
        }
        // If at main8, turn right to main9
        else if (_currentMain == 8) {
          _currentMain = 9;
        }
        // If at main11, turn right to main12
        else if (_currentMain == 11) {
          _currentMain = 12;
        }
      } else {
        // In reversed mode
        // If at main5_r, turn right to go to main4_r
        if (_currentMain == 5) {
          _currentMain = 4;
        }
      }
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  bool _shouldShowLeftTurnButton() {
    return (_isReversed &&
            _currentMain == 9 &&
            _currentFloor != 2) || // Disable left turn at 2main9_r
        (_isReversed && _currentMain == 12) ||
        (_isReversed && _currentMain == 15) ||
        (_isReversed &&
            _currentMain == 5 &&
            _currentFloor == 1) || // Add left turn at main5_r (first floor)
        (!_isReversed &&
            _currentMain == 4 &&
            _currentFloor != 2); // Disable left turn at 2main4
  }

  bool _shouldShowRightTurnButton() {
    // Remove right turn for 2main4
    return (_isReversed &&
            _currentMain == 5 &&
            _currentFloor !=
                2) || // Enable right turn at main5_r, disable at 2main5_r
        (!_isReversed &&
            _currentMain == 8 &&
            _currentFloor != 2) || // Disable right turn at 2main8
        (!_isReversed && _currentMain == 11) ||
        (!_isReversed && _currentMain == 15) ||
        (!_isReversed &&
            _currentMain == 4 &&
            _currentFloor != 2); // Only allow right turn at main4 if not 2main4
  }

  Widget _buildTurnButtons() {
    List<Widget> buttons = [];

    // Left turn button
    if (_shouldShowLeftTurnButton()) {
      buttons.add(
        Positioned(
          left: 16,
          bottom: MediaQuery.of(context).size.height * 0.35,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton(
                heroTag: 'leftTurnBtn',
                onPressed: _turnLeft,
                backgroundColor: Colors.white.withOpacity(0.7),
                child: Icon(Icons.turn_left, color: Colors.black),
              ),
              SizedBox(height: 4),
              Text('Turn Left',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)])),
            ],
          ),
        ),
      );
    }

    // Right turn button
    if (_shouldShowRightTurnButton()) {
      buttons.add(
        Positioned(
          right: 16,
          bottom: MediaQuery.of(context).size.height * 0.35,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FloatingActionButton(
                heroTag: 'rightTurnBtn',
                onPressed: _turnRight,
                backgroundColor: Colors.white.withOpacity(0.7),
                child: Icon(Icons.turn_right, color: Colors.black),
              ),
              SizedBox(height: 4),
              Text('Turn Right',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)])),
            ],
          ),
        ),
      );
    }

    if (buttons.isEmpty) {
      return Container();
    }

    return Stack(
      children: buttons,
    );
  }

  bool _shouldShowUTurnButton() {
    return _currentMain == 16;
  }

  void _performUTurn() {
    _showMain16DestinationDialog();
  }

  void _showMain16DestinationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Destination',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Where would you like to go?'),
                SizedBox(height: 20),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 15;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Demonstration Room',
                            style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 5;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen Hallway', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 4;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUTurnButton() {
    if (!_shouldShowUTurnButton()) {
      return Container();
    }

    return Positioned(
      bottom: 40,
      left: 16,
      child: FloatingActionButton(
        heroTag: 'uTurnBtn',
        onPressed: _performUTurn,
        backgroundColor: Colors.white.withOpacity(0.7),
        child: Icon(Icons.u_turn_left, color: Colors.black),
      ),
    );
  }

  Future<void> _precacheImages() async {
    // Precache all possible main images
    for (int main = 0; main <= 23; main++) {
      // Precache main image
      await precacheImage(AssetImage('assets/MAIN/main$main.jpg'), context);

      // Precache reversed version (except for main0 and main16 which don't have reversed versions)
      if (main != 0 && main != 16) {
        await precacheImage(
            AssetImage('assets/MAIN/main${main}_r.jpg'), context);
      }
    }
  }

  // Add method to move up from main9_r to 2main8
  void _moveUpToSecondFloor() {
    setState(() {
      _currentMain = 8;
      _currentFloor = 2; // Add this property if not already defined
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move from 2main5 to 2main6
  void _moveToSecondMain6() {
    setState(() {
      _currentMain = 6;
      _currentFloor = 2;
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move from 2main6_r to 2main5_r
  void _moveToSecondMain5Reversed() {
    setState(() {
      _currentMain = 5;
      _currentFloor = 2;
      _isReversed = true;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move down from 2main1_r to main15_r
  void _moveDownToFirstFloor() {
    setState(() {
      _currentMain = 15;
      _currentFloor = 1;
      _isReversed = true;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move up from main15 to 2main1
  void _moveUpFromMain15() {
    setState(() {
      _currentMain = 1;
      _currentFloor = 2;
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move up from 2main8 to 3main0
  void _moveUpToThirdFloor() {
    setState(() {
      _currentMain = 0;
      _currentFloor = 3;
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move up from main8 to 2main8_r
  void _moveUpFromMain8() {
    setState(() {
      _currentMain = 8;
      _currentFloor = 2;
      _isReversed = true; // Set to reversed when going up
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move down from 4main1_r to 3main0
  void _moveDownToThirdFloor() {
    setState(() {
      _currentMain = 0;
      _currentFloor = 3;
      _isReversed = false; // Set to normal when going down to 3main0
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  // Add method to move from 2main8 to 2main9
  void _moveToSecondMain9() {
    setState(() {
      _currentMain = 9;
      _currentFloor = 2;
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  void _moveUpFromMain1() {
    setState(() {
      _currentMain = 26;
      _currentFloor =
          1; // Remain on the first floor, unless main26 is on another floor
      _isReversed = false;
      _currentMapImage = _getMapImage();
    });
    _saveNavigationState();
  }

  List<String> _getLocationInfoList() {
    List<String> infoList = [];

    // First floor information
    if (_currentFloor == 1) {
      if (_currentMain == 1) {
        infoList.add('Accounting Office');
        infoList.add('Finance Office');
      }
      if (_currentMain == 3) {
        infoList.add('Guest Comfort Room');
      }
      if (_currentMain == 4) {
        infoList.add('Drinking Fountain');
        infoList.add('Reading Area');
      }
      if (_currentMain == 5) {
        infoList.add('Reading Area');
      }
      if (_currentMain == 15) {
        infoList.add('Consultation Room');
        infoList.add('Reading Area');
      }
      if (_currentMain == 18) {
        infoList.add('Comfort Room');
        infoList.add('Water Station');
      }
      if (_currentMain == 23) {
        infoList.add('BU Bahay na Bato');
      }
      if (_currentMain == 24) {
        infoList.add('BU Bahay na Bato');
      }
      if (_currentMain == 25) {
        infoList.add('Library 2nd Floor');
        infoList.add('Library 3rd Floor');
        infoList.add('Library 4th Floor');
        infoList.add('BU Bahay na Bato');
      }
      if (_currentMain == 26) {
        infoList.add('Center for Research and Publication');
        infoList.add('Executive Office');
      }
    }
    // 2nd floor information
    else if (_currentFloor == 2) {
      if (_currentMain == 6) {
        infoList.add('NSTP Office');
        infoList.add('Center for Student Development');
      }
    }
    // 4th floor information
    else if (_currentFloor == 3) {
      if (_currentMain == 1) {
        infoList.add('Nursing Skills Lab Discussion Room');
        infoList.add('Neo Natal Intensive Care Unit');
        infoList.add('Nursing Skills Laboratory 3');
      }
    }

    if (infoList.isEmpty) {
      infoList.add('Additional information not available for this location');
    }

    return infoList;
  }

  void _showLocationInfo() {
    List<String> infoList = _getLocationInfoList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              SizedBox(width: 8),
              Text(
                'Location Information',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: infoList.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 24),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(infoList[index]),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  // 1. Add a method to handle the turn left action at main5_r with confirmation dialog
  void _turnLeftFromMain5Reversed() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Destination',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Where would you like to go?'),
                SizedBox(height: 20),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 16;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Registrar', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 15;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Demonstration Room',
                            style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _turnRightFromMain4() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Destination',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Where would you like to go?'),
                SizedBox(height: 20),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 16;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Registrar', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 15;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Demonstration Room',
                            style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _turnLeftFromMain15Reversed() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Destination',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Where would you like to go?'),
                SizedBox(height: 20),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 16;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Registrar', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 5;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen Hallway', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 4;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _turnRightFromMain15() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Choose Destination',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Where would you like to go?'),
                SizedBox(height: 20),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 16;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Registrar', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 5;
                      _isReversed = false;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen Hallway', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _currentMain = 4;
                      _isReversed = true;
                      _currentMapImage = _getMapImage();
                    });
                    _saveNavigationState();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Row(
                      children: [
                        Icon(Icons.location_on, color: Colors.green, size: 24),
                        SizedBox(width: 8),
                        Text('Canteen', style: TextStyle(fontSize: 16)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancel', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showChangeBuildingDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Building Information',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Building information is only accessible through Building 2.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showChangeBuildingDialog1() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Building Information',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'Building information is only accessible through Building 2.',
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _navigateToBuilding2Selection() {
    // Save the target location in shared preferences
    SharedPreferences.getInstance().then((prefs) {
      prefs.setString('target_building', 'Main Campus: Building 2');
      prefs.setInt('target_floor', 2);
      prefs.setInt('target_clage', 1);
      prefs.setBool('target_reversed', true);

      // Navigate back to selection screen
      widget.onExit();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            color: Colors.green,
          ),
        ),
      );
    }

    // Calculate device dimensions for optimal caching
    final Size deviceSize = MediaQuery.of(context).size;
    final int cacheWidth = (deviceSize.width * 1.5).toInt();
    final int cacheHeight = (deviceSize.height * 1.5).toInt();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Building 1 Navigation',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.white,
          onPressed: () => widget.onExit(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            color: Colors.white,
            onPressed: _showLocationInfo,
            tooltip: 'Location Information',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Background map image with animation
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500), // Reduce from 500ms to 500ms
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: child, // Remove ScaleTransition for faster rendering
              );
            },
            child: Image.asset(
              _currentMapImage!,
              key: ValueKey(_currentMapImage),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.fill,
              cacheWidth: cacheWidth,
              cacheHeight: cacheHeight,
              gaplessPlayback:
                  true, // Prevents flickering between image changes
            ),
          ),

          // Location indicator
          Positioned(
            top: 16,
            left: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      constraints: BoxConstraints(
                        maxWidth: MediaQuery.of(context).size.width * 0.7,
                      ),
                      child: Text(
                        _getLocationName(),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.visible,
                        softWrap: true,
                      ),
                    ),
                    if (_currentMain != 0 &&
                        (_currentMain == 16 ||
                            _currentMain == 17 ||
                            _currentMain == 18 ||
                            _currentMain == 1 ||
                            _currentMain == 2 ||
                            ((_currentMain >= 3 && _currentMain <= 5) &&
                                _currentFloor == 1) ||
                            ((_currentMain >= 9 && _currentMain <= 11) &&
                                _currentFloor == 1) ||
                            (_currentMain == 21 && _currentFloor == 1) ||
                            (_currentMain == 23 && _currentFloor == 1)))
                      Container(
                        margin: EdgeInsets.only(left: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          icon: Icon(Icons.visibility, color: Colors.white),
                          onPressed: () {
                            String imagePath;
                            if (_currentMain == 16 ||
                                _currentMain == 17 ||
                                _currentMain == 18) {
                              imagePath = 'assets/IMAGES/registrar.jpg';
                            } else if (_currentMain == 1 || _currentMain == 2) {
                              imagePath = 'assets/IMAGES/finance.jpg';
                            } else if (_currentMain >= 3 && _currentMain <= 5) {
                              imagePath = 'assets/IMAGES/canteen.jpg';
                            } else if (_currentMain >= 9 &&
                                _currentMain <= 11) {
                              imagePath = 'assets/IMAGES/bookstore.jpg';
                            } else if (_currentMain == 21) {
                              imagePath = 'assets/IMAGES/alumni.JPG';
                            } else if (_currentMain == 23) {
                              imagePath = 'assets/IMAGES/bahaynabato.jpg';
                            } else {
                              return;
                            }
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return Dialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(20),
                                            topRight: Radius.circular(20),
                                          ),
                                          child: Image.asset(
                                            imagePath,
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          child: Text(
                                            (() {
                                              if (_currentMain == 0)
                                                return 'Accounting Office';
                                              if (_currentMain >= 3 &&
                                                  _currentMain <= 5)
                                                return 'Canteen';
                                              if (_currentMain >= 16 &&
                                                  _currentMain <= 18)
                                                return 'Registrar Office';
                                              if (_currentMain >= 21 &&
                                                  _currentMain <= 22)
                                                return 'Alumni Office';
                                              if (_currentMain == 23)
                                                return 'Bahay na Bato';
                                              if (_currentMain >= 9 &&
                                                  _currentMain <= 11)
                                                return 'Book Store';
                                              if (_currentMain == 1 ||
                                                  _currentMain == 2)
                                                return 'Finance Office';
                                              return '';
                                            })(),
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Colors.black
                                                  : Theme.of(context)
                                                      .textTheme
                                                      .bodyLarge
                                                      ?.color,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(5.0),
                                          child: TextButton(
                                            onPressed: () =>
                                                Navigator.of(context).pop(),
                                            child: Text('Close',
                                                style: TextStyle(
                                                    color: Colors.green,
                                                    fontSize: 16)),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                  ],
                ),
                if (_currentMain == 0)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: IconButton(
                        icon: Icon(Icons.visibility, color: Colors.white),
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return Dialog(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        spreadRadius: 2,
                                        blurRadius: 5,
                                        offset: Offset(0, 3),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(20),
                                          topRight: Radius.circular(20),
                                        ),
                                        child: Image.asset(
                                          'assets/IMAGES/accounting.jpg',
                                          fit: BoxFit.contain,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 8.0),
                                        child: Text(
                                          'Accounting Office',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18,
                                            color:
                                                Theme.of(context).brightness ==
                                                        Brightness.dark
                                                    ? Colors.black
                                                    : Theme.of(context)
                                                        .textTheme
                                                        .bodyLarge
                                                        ?.color,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.all(5.0),
                                        child: TextButton(
                                          onPressed: () =>
                                              Navigator.of(context).pop(),
                                          child: Text('Close',
                                              style: TextStyle(
                                                  color: Colors.green,
                                                  fontSize: 16)),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Reverse indicator - simplified to just show the icon
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _isReversed ? Colors.yellow : Colors.grey,
                  width: 2,
                ),
              ),
              child: Icon(
                Icons.autorenew,
                color: _isReversed ? Colors.yellow : Colors.grey,
                size: 24,
              ),
            ),
          ),

          // Turn buttons
          _buildTurnButtons(),

          // Add turn left button for 2main5
          if (!_isReversed && _currentMain == 5 && _currentFloor == 2)
            Positioned(
              left: 16,
              bottom: MediaQuery.of(context).size.height * 0.35,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  FloatingActionButton(
                    heroTag: 'moveToSecondMain6Btn',
                    onPressed: _moveToSecondMain6,
                    backgroundColor: Colors.white.withOpacity(0.7),
                    child: Icon(Icons.turn_left, color: Colors.black),
                  ),
                  SizedBox(height: 4),
                  Text('Turn Left',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(color: Colors.black, blurRadius: 2)
                          ])),
                ],
              ),
            ),

          // Add turn right button for 2main6_r
          if (_isReversed && _currentMain == 6 && _currentFloor == 2)
            Positioned(
              right: 16,
              bottom: MediaQuery.of(context).size.height * 0.35,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  FloatingActionButton(
                    heroTag: 'moveToSecondMain5ReversedBtn',
                    onPressed: _moveToSecondMain5Reversed,
                    backgroundColor: Colors.white.withOpacity(0.7),
                    child: Icon(Icons.turn_right, color: Colors.black),
                  ),
                  SizedBox(height: 4),
                  Text('Turn Right',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(color: Colors.black, blurRadius: 2)
                          ])),
                ],
              ),
            ),

          // Add down button for 2main1_r - responsive positioning
          if (_isReversed && _currentMain == 1 && _currentFloor == 2)
            Stack(
              children: [
                // Centered down button
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'downToFirstFloorBtn',
                          onPressed: _moveDownToFirstFloor,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Transform.rotate(
                            angle: 3.14159, // 180 degrees in radians (π)
                            child: Icon(Icons.stairs_outlined,
                                color: Colors.black),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Go Down',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Bottom right - Vertical stack of Forward, Reverse, Backward
                Positioned(
                  bottom: 40,
                  right: 30,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Forward button
                      FloatingActionButton(
                        heroTag: 'forwardBtn2main1r',
                        onPressed: _moveForward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child:
                            Icon(Icons.keyboard_arrow_up, color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Forward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                      SizedBox(height: 10),
                      // Reverse button
                      FloatingActionButton(
                        heroTag: 'reverseBtn2main1r',
                        onPressed: _toggleReverse,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.autorenew,
                            color: Colors.black, size: 30),
                      ),
                      SizedBox(height: 4),
                      Text('Reverse',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                      SizedBox(height: 10),
                      // Backward button
                      FloatingActionButton(
                        heroTag: 'backBtn2main1r',
                        onPressed: _moveBackward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.keyboard_arrow_down,
                            color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Backward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),
                ),
              ],
            ),

          // Navigation buttons - hide when at 2main1_r to avoid overlap
          if (!(_isReversed && _currentMain == 1 && _currentFloor == 2))
            Stack(
              children: [
                // Left side - Down button
                Positioned(
                  bottom: 40,
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Down button
                      Column(mainAxisSize: MainAxisSize.min, children: [
                        if (_currentFloor == 3 && _currentMain == 0) ...[
                          FloatingActionButton(
                            heroTag: 'downFrom3Main0Btn',
                            onPressed: () {
                              setState(() {
                                _currentFloor = 2;
                                _currentMain = 8;
                                _isReversed = false;
                                _currentMapImage = _getMapImage();
                              });
                              _saveNavigationState();
                            },
                            backgroundColor: Colors.white.withOpacity(0.7),
                            child: Transform.rotate(
                              angle: 3.14159, // 180 degrees in radians (π)
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Go Down',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ],
                            ),
                          ),
                        ] else if (_isReversed &&
                            _currentMain == 1 &&
                            _currentFloor == 4) ...[
                          FloatingActionButton(
                            heroTag: 'downFrom4Main1Btn',
                            onPressed: _moveDownToThirdFloor,
                            backgroundColor: Colors.white.withOpacity(0.7),
                            child: Transform.rotate(
                              angle: 3.14159, // 180 degrees in radians (π)
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Go Down',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ],
                            ),
                          ),
                        ] else if (!_isReversed &&
                            _currentMain == 8 &&
                            _currentFloor == 2) ...[
                          FloatingActionButton(
                            heroTag: 'downFrom2Main8Btn',
                            onPressed: () {
                              setState(() {
                                _currentFloor = 1;
                                _currentMain = 8;
                                _isReversed = true;
                                _currentMapImage = _getMapImage();
                              });
                              _saveNavigationState();
                            },
                            backgroundColor: Colors.white.withOpacity(0.7),
                            child: Transform.rotate(
                              angle: 3.14159, // 180 degrees in radians (π)
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Go Down',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ],
                            ),
                          ),
                        ] else if (_isReversed && _currentMain == 26) ...[
                          FloatingActionButton(
                            heroTag: 'downFromMain26rBtn',
                            onPressed: () {
                              setState(() {
                                _currentMain = 1;
                                _isReversed = true;
                                _currentMapImage = _getMapImage();
                              });
                              _saveNavigationState();
                            },
                            backgroundColor: Colors.white.withOpacity(0.7),
                            child: Transform.rotate(
                              angle: 3.14159, // 180 degrees in radians (π)
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Go Down',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ],
                            ),
                          ),
                        ],
                      ]),

                      // Spacing between buttons
                      SizedBox(width: 40),

                      // Right side - Up button
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (!_isReversed &&
                              _currentMain == 8 &&
                              _currentFloor == 1) ...[
                            FloatingActionButton(
                              heroTag: 'upFromMain8Btn',
                              onPressed: _moveUpFromMain8,
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ] else if (_isReversed &&
                              _currentMain == 9 &&
                              _currentFloor == 1) ...[
                            FloatingActionButton(
                              heroTag: 'upFromMain9rBtn',
                              onPressed: _moveUpToSecondFloor,
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ] else if (_isReversed &&
                              _currentMain == 2 &&
                              _currentFloor == 1) ...[
                            FloatingActionButton(
                              heroTag: 'upToMain26Btn',
                              onPressed: () {
                                setState(() {
                                  _currentMain = 26;
                                  _isReversed = false;
                                  _currentMapImage = _getMapImage();
                                });
                                _saveNavigationState();
                              },
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ] else if (!_isReversed &&
                              _currentMain == 15 &&
                              _currentFloor == 1) ...[
                            FloatingActionButton(
                              heroTag: 'upFromMain15Btn',
                              onPressed: _moveUpFromMain15,
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ] else if (!_isReversed &&
                              _currentMain == 1 &&
                              _currentFloor == 1) ...[
                            FloatingActionButton(
                              heroTag: 'upFromMain1Btn',
                              onPressed: _moveUpFromMain1,
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ] else if (!_isReversed &&
                              _currentMain == 8 &&
                              _currentFloor == 2) ...[
                            FloatingActionButton(
                              heroTag: 'upFrom2Main8Btn',
                              onPressed: _moveUpToThirdFloor,
                              backgroundColor: Colors.white.withOpacity(0.7),
                              child: Icon(Icons.stairs_outlined,
                                  color: Colors.black),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Go Up',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                // Hallway on the right message
                if (!_isReversed &&
                    ((_currentFloor == 2 && _currentMain == 8) ||
                        (_currentFloor == 3 && _currentMain == 0)))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Hallway on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Finance on the left message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 1))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Finance office on the left',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Finance on the right message
                if (_isReversed && (_currentFloor == 1 && _currentMain == 2))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Finance office on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Reading area on the left message
                if (_isReversed &&
                    ((_currentFloor == 1 && _currentMain == 5) ||
                        (_currentFloor == 1 && _currentMain == 15)))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Reading area on the left',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Reading area just ahead message
                if ((!_isReversed &&
                        (_currentFloor == 1 && _currentMain == 4)) ||
                    (_isReversed && (_currentFloor == 1 && _currentMain == 17)))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Reading area just ahead',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Bahay na bato just ahead message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 23))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Bahay na bato just ahead',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Bahay na bato on the left message
                if (_isReversed && (_currentFloor == 1 && _currentMain == 25))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Bahay na bato on the left',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Constultation room just ahead message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 15))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Consultation room just ahead',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Admissions office just ahead message
                if (_isReversed && (_currentFloor == 1 && _currentMain == 1))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Admisisons office just ahead',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Accounting office on the left message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 0))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        width: 275,
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Accounting office on the left, beside the main gate ',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Comfort room on the left message
                if (!_isReversed &&
                    ((_currentFloor == 1 && _currentMain == 3) ||
                        (_currentFloor == 1 && _currentMain == 18)))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Comfort room on the left',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Comfort room on the right message
                if (_isReversed &&
                    ((_currentFloor == 1 && _currentMain == 3) ||
                        (_isReversed &&
                            (_currentFloor == 1 && _currentMain == 19))))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Comfort room on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Bookstore on the right message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 8))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Bookstore on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Center for Community Services - HALINA just ahead message
                if (!_isReversed && (_currentFloor == 2 && _currentMain == 5))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        width: 250,
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Center for Community Services - HALINA just ahead',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // ALumni office on the left message
                if (!_isReversed && (_currentFloor == 1 && _currentMain == 21))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Alumni office on the left',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // ALumni office on the right message
                if (_isReversed && (_currentFloor == 1 && _currentMain == 23))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          textAlign: TextAlign.center,
                          'Alumni office on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Stairs on the right message
                if (_isReversed &&
                    ((_currentFloor == 2 && _currentMain == 9) ||
                        (_currentFloor == 3 && _currentMain == 1)))
                  Positioned(
                    top: 100,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          'Stairs on the right',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                            shadows: [
                              Shadow(color: Colors.black, blurRadius: 2)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                // Bottom right - Vertical stack of Forward, Reverse, Backward
                Positioned(
                  bottom: 40,
                  right: 16,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Forward button and label
                      if (!((!_isReversed &&
                              ((_currentMain == 4 && _currentFloor != 2) ||
                                  (_currentMain == 5 && _currentFloor == 2) ||
                                  (_currentMain == 8 && _currentFloor != 2) ||
                                  _currentMain == 11 ||
                                  _currentMain == 15 ||
                                  _currentMain == 25 ||
                                  _currentMain == 26)) ||
                          (_isReversed &&
                              ((_currentMain == 5 && _currentFloor != 2) ||
                                  (_currentMain == 9 && _currentFloor != 2) ||
                                  _currentMain == 12 ||
                                  _currentMain == 26 ||
                                  (_currentFloor == 2 &&
                                      _currentMain == 6))))) ...[
                        FloatingActionButton(
                          heroTag: 'forwardBtn',
                          onPressed: _moveForward,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Icon(Icons.keyboard_arrow_up,
                              color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Forward',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                        SizedBox(height: 10),
                      ],
                      // Reverse button and label
                      if (_currentMain == 16) ...[
                        FloatingActionButton(
                          heroTag: 'uTurnBtn',
                          onPressed: _performUTurn,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Icon(Icons.u_turn_left, color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Turn Back',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                        SizedBox(height: 10),
                      ] else if (_currentMain != 0 ||
                          (_currentFloor > 2 &&
                              !(_currentFloor == 3 && _currentMain == 0))) ...[
                        FloatingActionButton(
                          heroTag: 'reverseBtn',
                          onPressed: _toggleReverse,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Icon(Icons.autorenew,
                              color: Colors.black, size: 30),
                        ),
                        SizedBox(height: 4),
                        Text('Reverse',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                        SizedBox(height: 10),
                      ],
                      // Backward button and label
                      if (_currentMain != 0 &&
                              _currentMain != 16 &&
                              !(_currentMain == 26 && !_isReversed) &&
                              !(_currentFloor == 2 &&
                                  _currentMain == 1 &&
                                  !_isReversed) &&
                              !(_currentFloor == 2 &&
                                  _currentMain == 6 &&
                                  !_isReversed) &&
                              !(_currentFloor == 2 &&
                                  _currentMain == 5 &&
                                  _isReversed) &&
                              !(_isReversed && _currentMain == 25) &&
                              !(_isReversed &&
                                  _currentMain ==
                                      26) && // Hide backward at main26_r
                              !(_isReversed &&
                                  (_currentMain == 8 ||
                                      _currentMain == 11 ||
                                      _currentMain == 15 ||
                                      (_currentMain == 4 &&
                                          _currentFloor == 1))) &&
                              !(!_isReversed &&
                                  (_currentMain == 12 ||
                                      (_currentMain == 5 &&
                                          _currentFloor == 1))) &&
                              !(_isReversed &&
                                  _currentMain == 1 &&
                                  _currentFloor == 3) &&
                              !(!_isReversed &&
                                  _currentMain == 9 &&
                                  _currentFloor ==
                                      1) // Hide on main9 normal view
                          ) ...[
                        FloatingActionButton(
                          heroTag: 'backBtn',
                          onPressed: () {
                            if (_currentFloor == 2 &&
                                _currentMain == 9 &&
                                !_isReversed) {
                              // Special case: go back to 2main8
                              setState(() {
                                _currentMain = 8;
                                _currentMapImage = _getMapImage();
                              });
                            } else {
                              _moveBackward();
                            }
                            _saveNavigationState();
                          },
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Icon(Icons.keyboard_arrow_down,
                              color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Backward',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store a reference to the context that can be safely used later
    _safeContext = context;
  }

  @override
  void dispose() {
    // Only save state if we're not resetting
    if (!widget.shouldResetState) {
      _saveNavigationState();
    } else {
      _clearNavigationState();
    }

    // Don't use context in dispose
    _safeContext = null;

    super.dispose();
  }
}
