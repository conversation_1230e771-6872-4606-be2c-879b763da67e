import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class LocalDatabaseHelper {
  static final LocalDatabaseHelper _instance = LocalDatabaseHelper._internal();
  static Database? _database;

  factory LocalDatabaseHelper() => _instance;

  LocalDatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'tasks_database.db');
    return await openDatabase(
      path,
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE tasks(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT,
        task TEXT,
        completed TEXT,
        deadline TEXT,
        notify TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE deleted_tasks(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT,
        task TEXT,
        deleted_at TEXT,
        is_synced INTEGER DEFAULT 0
      )
    ''');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add new columns if they don't exist
      try {
        await db.execute('ALTER TABLE tasks ADD COLUMN created_at TEXT');
        await db.execute(
            'ALTER TABLE tasks ADD COLUMN is_synced INTEGER DEFAULT 0');
        await db.execute(
            'ALTER TABLE deleted_tasks ADD COLUMN is_synced INTEGER DEFAULT 0');
      } catch (e) {
        print('Error upgrading database: $e');
      }
    }
  }

  // Insert a task
  Future<int> insertTask(Map<String, dynamic> task) async {
    final db = await database;
    if (!task.containsKey('created_at')) {
      task['created_at'] = DateTime.now().toIso8601String();
    }
    if (!task.containsKey('is_synced')) {
      task['is_synced'] = 0;
    }
    return await db.insert('tasks', task);
  }

  // Get all tasks
  Future<List<Map<String, dynamic>>> getTasks(String email) async {
    final db = await database;
    return await db.query(
      'tasks',
      where: 'email = ?',
      whereArgs: [email],
      orderBy: 'created_at DESC',
    );
  }

  // Update a task
  Future<int> updateTask(Map<String, dynamic> task) async {
    final db = await database;
    return await db.update(
      'tasks',
      task,
      where: 'id = ?',
      whereArgs: [task['id']],
    );
  }

  // Delete a task
  Future<int> deleteTask(int id) async {
    final db = await database;
    final task = await db.query('tasks', where: 'id = ?', whereArgs: [id]);
    if (task.isNotEmpty) {
      try {
        await db.insert('deleted_tasks', {
          'email': task[0]['email'],
          'task': task[0]['task'],
          'deleted_at': DateTime.now().toIso8601String(),
          'is_synced': 0,
        });
      } catch (e) {
        print('Error inserting into deleted_tasks: $e');
      }
    }
    return await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get unsynced tasks
  Future<List<Map<String, dynamic>>> getUnsyncedTasks() async {
    final db = await database;
    return await db.query(
      'tasks',
      where: 'is_synced = ?',
      whereArgs: [0],
      orderBy: 'created_at DESC',
    );
  }

  // Mark task as synced
  Future<int> markTaskAsSynced(int id) async {
    final db = await database;
    return await db.update(
      'tasks',
      {'is_synced': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Delete all tasks for a user
  Future<int> deleteAllTasks(String email) async {
    final db = await database;
    return await db.delete(
      'tasks',
      where: 'email = ?',
      whereArgs: [email],
    );
  }

  Future<void> markTaskAsDeleted(String email, String task) async {
    final db = await database;
    await db.insert('deleted_tasks', {
      'email': email,
      'task': task,
      'deleted_at': DateTime.now().toIso8601String(),
    });
  }

  Future<List<Map<String, dynamic>>> getDeletedTasks() async {
    final db = await database;
    return await db.query('deleted_tasks');
  }

  Future<void> clearDeletedTasks() async {
    final db = await database;
    await db.delete('deleted_tasks');
  }

  // Get unsynced deleted tasks
  Future<List<Map<String, dynamic>>> getUnsyncedDeletedTasks() async {
    final db = await database;
    try {
      return await db.query(
        'deleted_tasks',
        where: 'is_synced = ?',
        whereArgs: [0],
      );
    } catch (e) {
      // If is_synced column doesn't exist, return all deleted tasks
      return await db.query('deleted_tasks');
    }
  }

  // Mark deleted task as synced
  Future<int> markDeletedTaskAsSynced(int id) async {
    final db = await database;
    try {
      return await db.update(
        'deleted_tasks',
        {'is_synced': 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      // If is_synced column doesn't exist, just delete the record
      return await db.delete(
        'deleted_tasks',
        where: 'id = ?',
        whereArgs: [id],
      );
    }
  }
}
