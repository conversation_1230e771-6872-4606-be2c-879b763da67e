import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_init;
import 'package:test_1_copy_auth_events/services/auth_service.dart';
import 'dart:convert';

class TaskNotificationService {
  static final TaskNotificationService _instance =
      TaskNotificationService._internal();
  factory TaskNotificationService() => _instance;
  TaskNotificationService._internal();

  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final AuthService _authService = AuthService();

  Timer? _notificationTimer;
  bool _isInitialized = false;

  // Set to track notification IDs that have been sent to avoid duplicates
  final Set<String> _sentNotifications = {};

  // Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize timezone data
    tz_init.initializeTimeZones();

    // Initialize notification settings
    const AndroidInitializationSettings initSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initSettingsAndroid,
      iOS: initSettingsIOS,
    );

    await notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) {},
    );

    _isInitialized = true;

    // Start the periodic checker
    startPeriodicChecks();
  }

  // Start checking for notifications periodically
  void startPeriodicChecks() {
    // Cancel any existing timer
    _notificationTimer?.cancel();

    // Set up a timer that runs every 15 seconds instead of every millisecond
    _notificationTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      checkForDueNotifications();
    });
  }

  // Stop periodic checks
  void stopPeriodicChecks() {
    _notificationTimer?.cancel();
    _notificationTimer = null;
  }

  // Check for notifications that should be displayed now
  Future<void> checkForDueNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = _authService.userEmail;

    // Skip if user is not logged in
    if (userEmail == null) return;

    try {
      // Get cached tasks first for speed
      final cachedTasksJson = prefs.getString('cached_tasks_${userEmail}');
      List<Map<String, dynamic>> tasks = [];

      if (cachedTasksJson != null) {
        // Parse cached tasks
        tasks = (json.decode(cachedTasksJson) as List).map((task) {
          final Map<String, dynamic> taskMap = Map<String, dynamic>.from(task);

          // Convert string dates back to DateTime objects
          if (taskMap['deadlineDate'] != null &&
              taskMap['deadlineDate'] is String) {
            taskMap['deadlineDate'] = DateTime.parse(taskMap['deadlineDate']);
          }

          if (taskMap['notifyDate'] != null &&
              taskMap['notifyDate'] is String) {
            taskMap['notifyDate'] = DateTime.parse(taskMap['notifyDate']);
          }

          return taskMap;
        }).toList();
      } else {
        // Fallback to fetching from service if cache is unavailable
        final fetchedTasks = await _authService.fetchUserTasks(userEmail);

        tasks = fetchedTasks.map((task) {
          DateTime? deadlineDate;
          if (task['deadline'] != null &&
              task['deadline'].toString().isNotEmpty) {
            try {
              deadlineDate = DateTime.parse(task['deadline'].toString());
            } catch (e) {
              print('Error parsing deadline date: ${task['deadline']} - $e');
            }
          }

          DateTime? notifyDate;
          if (task['notify'] != null && task['notify'].toString().isNotEmpty) {
            try {
              notifyDate = DateTime.parse(task['notify'].toString());
            } catch (e) {
              print('Error parsing notify date: ${task['notify']} - $e');
            }
          }

          return {
            'text': task['task'],
            'isChecked': task['completed'] == 'y',
            'deadlineDate': deadlineDate,
            'notifyDate': notifyDate,
          };
        }).toList();
      }

      // Find tasks that should trigger notifications right now
      await checkAndShowDueNotifications(tasks);

      // NEW: Also check for upcoming and overdue tasks
      await checkAndShowDueTaskReminders(tasks);
    } catch (e) {
      print('Error checking for due notifications: $e');
    }
  }

  // Check and show notifications for tasks due now
  Future<void> checkAndShowDueNotifications(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();

    // Filter for uncompleted tasks with notify dates that match current time
    final dueTasks = tasks.where((task) {
      if (task['isChecked'] == true || task['notifyDate'] == null) return false;

      final notifyDate = task['notifyDate'] as DateTime;
      final taskText = task['text'] ?? 'Task';
      final notifyId =
          'notify_${taskText.hashCode}_${notifyDate.year}${notifyDate.month}${notifyDate.day}${notifyDate.hour}${notifyDate.minute}';

      // Check if notification time matches current time (within the minute)
      // AND we haven't already sent this notification
      if (notifyDate.year == now.year &&
          notifyDate.month == now.month &&
          notifyDate.day == now.day &&
          notifyDate.hour == now.hour &&
          notifyDate.minute == now.minute) {
        // Only return true if we haven't sent this notification yet
        if (!_sentNotifications.contains(notifyId)) {
          _sentNotifications.add(notifyId);

          // Clean up the set after some time
          Timer(const Duration(minutes: 2), () {
            _sentNotifications.remove(notifyId);
          });

          return true;
        }
      }

      return false;
    }).toList();
  }

  // NEW METHOD: Check for tasks that are approaching their deadline or are overdue
  Future<void> checkAndShowDueTaskReminders(
      List<Map<String, dynamic>> tasks) async {
    final now = DateTime.now();

    // Process each task
    for (var task in tasks) {
      if (task['isChecked'] == true || task['deadlineDate'] == null) continue;

      final deadlineDate = task['deadlineDate'] as DateTime;

      // Create unique identifiers for this task's notifications
      final taskText = task['text'] ?? 'Task';
      final taskId = '${taskText.hashCode}';
      final upcomingId = 'upcoming_$taskId';
      final overdueId = 'overdue_$taskId';

      // Calculate time difference in minutes
      final differenceInMinutes = deadlineDate.difference(now).inMinutes;

      // Case 1: Task is due in exactly 30 minutes
      if (differenceInMinutes == 30) {
        // Check if we haven't sent this notification yet
        if (!_sentNotifications.contains(upcomingId)) {
          await showUpcomingDueNotification(task);
          _sentNotifications.add(upcomingId);

          // Clean up the set after some time to prevent memory leaks
          Timer(const Duration(minutes: 31), () {
            _sentNotifications.remove(upcomingId);
          });
        }
      }

      // Case 2: Task is 5 minutes overdue
      else if (differenceInMinutes == -5) {
        // Check if we haven't sent this notification yet
        if (!_sentNotifications.contains(overdueId)) {
          await showOverdueNotification(task);
          _sentNotifications.add(overdueId);

          // Clean up the set after some time
          Timer(const Duration(minutes: 6), () {
            _sentNotifications.remove(overdueId);
          });
        }
      }
    }
  }

  // NEW METHOD: Show notification for tasks due in 30 minutes
  Future<void> showUpcomingDueNotification(Map<String, dynamic> task) async {
    final String taskText = task['text'] ?? 'Task reminder';
    final deadlineDate = task['deadlineDate'] as DateTime;
    final formattedTime = _formatTime(deadlineDate);

    String notificationBody =
        'Task is due in 30 minutes at $formattedTime! Make sure you finish it asap!⏳';

    await showNotification(
      title: 'BUtility: Upcoming Task - "$taskText"',
      body: notificationBody,
      channelId: 'upcoming_due_channel',
      channelName: 'Upcoming Due Tasks',
      channelDescription: 'Notifications for tasks due in 30 minutes',
      color: Colors.orange,
    );
  }

  // NEW METHOD: Show notification for overdue tasks
  Future<void> showOverdueNotification(Map<String, dynamic> task) async {
    final String taskText = task['text'] ?? 'Task reminder';
    final deadlineDate = task['deadlineDate'] as DateTime;
    final formattedTime = _formatTime(deadlineDate);

    String notificationBody =
        'Task was due at $formattedTime and is now overdue!⚠️';

    await showNotification(
      title: 'BUtility: Overdue Task - "$taskText"',
      body: notificationBody,
      channelId: 'overdue_channel',
      channelName: 'Overdue Tasks',
      channelDescription: 'Notifications for tasks that are overdue',
      color: Colors.red,
    );
  }

  // Helper method to format time
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12
        ? dateTime.hour - 12
        : (dateTime.hour == 0 ? 12 : dateTime.hour);
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute $period';
  }

  // Enhanced notification method with customizable channel
  Future<void> showNotification({
    int id = 0,
    String? title,
    String? body,
    String channelId = 'task_reminder_channel',
    String channelName = 'Task Reminders',
    String channelDescription = 'Channel for task reminder notifications',
    Color color = Colors.green,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      channelName,
      channelDescription: channelDescription,
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      color: color,
      colorized: true,
    );

    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // Generate a unique ID for each notification based on current time
    final uniqueId = DateTime.now().millisecondsSinceEpoch % 10000;

    await notificationsPlugin.show(
      uniqueId,
      title,
      body,
      details,
    );
  }

  // Clean up resources
  void dispose() {
    stopPeriodicChecks();
    _sentNotifications.clear();
  }
}
