import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_init;
import 'package:test_1_copy_auth_events/services/auth_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotiService {
  final FlutterLocalNotificationsPlugin notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final AuthService _authService = AuthService();
  bool _isInitialized = false;

  Future<void> initNotification() async {
    if (_isInitialized) return;

    tz_init.initializeTimeZones();

    const AndroidInitializationSettings initSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initSettingsAndroid,
      iOS: initSettingsIOS,
    );

    await notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (details) {},
    );

    _isInitialized = true;
    _checkInitialNotifications();
  }

  Future<void> _checkInitialNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString('userEmail');
    final isGuest = prefs.getBool('isGuest') ?? false;

    if (email != null) {
      final userDetails = await _authService.fetchUserDetails(email);
      if (userDetails != null) {
        final events = await _authService.fetchEvents(
          userDetails['department'],
          userDetails['course'],
        );
        final tasks = await _authService.fetchUserTasks(email);

        await _showEventNotification(events);
        await Future.delayed(const Duration(seconds: 3));
        await _showOngoingTaskNotification(tasks);
        await Future.delayed(const Duration(seconds: 3));
        await _checkDueTasks(tasks);
        await Future.delayed(const Duration(seconds: 3));
        await _checkNotifyTasks(tasks);
      }
    } else if (isGuest) {
      final events = await _authService.fetchEvents('', '', isGuest: true);
      await _showEventNotification(
          events.where((e) => e['forguest'] == 'y').toList());
    }
  }

  Future<void> _showEventNotification(List events) async {
    int todayEventCount = events.where((event) {
      DateTime eventDate = DateTime.parse(event['date']);
      DateTime currentDate = DateTime.now();
      return eventDate.year == currentDate.year &&
          eventDate.month == currentDate.month &&
          eventDate.day == currentDate.day;
    }).length;

    String eventMessage = todayEventCount > 0
        ? 'You’ve got $todayEventCount event${todayEventCount != 1 ? 's' : ''} lined up for today! Check it out now! 🗓️'
        : 'Enjoy a free day—no events scheduled today!';

    await showNotification(
      id: 1,
      title: 'BUtility: Events',
      body: eventMessage,
    );
  }

  Future<void> _showOngoingTaskNotification(List tasks) async {
    int ongoingCount = tasks.where((t) => t['completed'] != 'y').length;

    String message = ongoingCount > 0
        ? 'You’re working on $ongoingCount task${ongoingCount != 1 ? 's' : ''}. You’ve got this! 💪'
        : 'Nice work! All tasks completed.';

    await showNotification(
      id: 2,
      title: 'BUtility: Ongoing Tasks',
      body: message,
    );
  }

  Future<void> _checkDueTasks(List tasks) async {
    final today = DateTime.now();
    final dueTasks = tasks.where((task) {
      if (task['deadline'] == null || task['completed'] == 'y') return false;
      try {
        final deadline = DateTime.parse(task['deadline'].toString());
        return deadline.year == today.year &&
            deadline.month == today.month &&
            deadline.day == today.day;
      } catch (e) {
        return false;
      }
    }).toList();

    if (dueTasks.isNotEmpty) {
      String taskNames = dueTasks.map((t) => t['task']).join(', ');
      String message = dueTasks.length == 1
          ? '"$taskNames" is due today! Please make sure to complete it asap!⏳'
          : 'You’ve got ${dueTasks.length} tasks due today: $taskNames. Let’s do this!';

      await showNotification(
        id: 3,
        title: 'BUtility: Due Tasks',
        body: message,
      );
    }
  }

  Future<void> _checkNotifyTasks(List tasks) async {
    final now = DateTime.now();
    final notifyTasks = tasks.where((task) {
      if (task['notifyDate'] == null || task['isChecked'] == true) return false;
      try {
        final notifyDate = task['notifyDate'] as DateTime;
        return notifyDate.year == now.year &&
            notifyDate.month == now.month &&
            notifyDate.day == now.day &&
            notifyDate.hour == now.hour &&
            notifyDate.minute == now.minute;
      } catch (e) {
        return false;
      }
    }).toList();

    if (notifyTasks.isNotEmpty) {
      String taskNames = notifyTasks.map((t) => t['text']).join(', ');
      String message = notifyTasks.length == 1
          ? 'Reminder: "$taskNames" is scheduled for now!'
          : 'You have ${notifyTasks.length} task reminders now: $taskNames';

      await showNotification(
        id: 4, // Different ID from due tasks
        title: 'BUtility: Task Reminders',
        body: message,
      );
    }
  }

  NotificationDetails notificationDetails() {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'task_reminder_channel',
      'Task Reminders',
      channelDescription: 'Channel for task reminder notifications',
      importance: Importance.high,
      priority: Priority.high,
      playSound: true,
      enableVibration: true,
      color: Colors.green,
      colorized: true,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    return const NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
  }

  Future<void> showNotification({
    int id = 0,
    String? title,
    String? body,
  }) async {
    await notificationsPlugin.show(
      id,
      title,
      body,
      notificationDetails(),
    );
  }
}
