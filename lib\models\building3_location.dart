class Building3Location {
  final String id;
  final String name;
  final String displayName;
  final int floor;
  final int cite;
  final bool isReversed;
  final List<String> keywords;
  final List<String> aliases;

  const Building3Location({
    required this.id,
    required this.name,
    required this.displayName,
    required this.floor,
    required this.cite,
    required this.isReversed,
    this.keywords = const [],
    this.aliases = const [],
  });

  @override
  String toString() => displayName;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Building3Location &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class Building3LocationData {
  static const List<Building3Location> allLocations = [
    // Floor 1
    Building3Location(
      id: 'f1_c0',
      name: 'ITB First Floor',
      displayName: 'ITB First Floor',
      floor: 1,
      cite: 0,
      isReversed: false,
      keywords: ['first', 'ground', 'entrance', 'stairs'],
      aliases: ['1st floor', 'ground floor'],
    ),
    Building3Location(
      id: 'f1_c1',
      name: 'ITB 101',
      displayName: 'ITB 101',
      floor: 1,
      cite: 1,
      isReversed: false,
      keywords: ['101', 'classroom', 'room'],
      aliases: ['room 101', 'classroom 101'],
    ),
    Building3Location(
      id: 'f1_c2',
      name: 'ITB 102',
      displayName: 'ITB 102',
      floor: 1,
      cite: 2,
      isReversed: false,
      keywords: ['102', 'classroom', 'room'],
      aliases: ['room 102', 'classroom 102'],
    ),
    Building3Location(
      id: 'f1_c3',
      name: 'ITB 103',
      displayName: 'ITB 103',
      floor: 1,
      cite: 3,
      isReversed: false,
      keywords: ['103'],
      aliases: ['ITB 103'],
    ),
    Building3Location(
      id: 'f1_c4',
      name: 'ITB 104',
      displayName: 'ITB 104',
      floor: 1,
      cite: 4,
      isReversed: false,
      keywords: ['104', 'classroom', 'room', 'comfort room'],
      aliases: ['room 104', 'classroom 104'],
    ),

    // Floor 2
    Building3Location(
      id: 'f2_c0',
      name: 'ITB Second Floor',
      displayName: 'ITB Second Floor',
      floor: 2,
      cite: 0,
      isReversed: false,
      keywords: [
        'second',
        'stairs',
        'cits',
        'computer',
        'information',
        'technology'
      ],
      aliases: ['2nd floor'],
    ),
    Building3Location(
      id: 'f2_c1',
      name: 'ITB 201',
      displayName: 'ITB 201',
      floor: 2,
      cite: 1,
      isReversed: false,
      keywords: ['201', 'classroom', 'room', 'mac', 'laboratory'],
      aliases: ['room 201', 'classroom 201', 'mac lab'],
    ),
    Building3Location(
      id: 'f2_c2',
      name: 'ITB 202',
      displayName: 'ITB 202',
      floor: 2,
      cite: 2,
      isReversed: false,
      keywords: ['202', 'classroom', 'room'],
      aliases: ['room 202', 'classroom 202'],
    ),
    Building3Location(
      id: 'f2_c3',
      name: 'Center for Career and Counseling',
      displayName: 'Center for Career and Counseling',
      floor: 2,
      cite: 3,
      isReversed: false,
      keywords: [
        'career',
        'counseling',
        'center',
        'guidance',
        'cisco',
        'laboratory'
      ],
      aliases: ['career center', 'counseling center', 'cisco lab'],
    ),
    Building3Location(
      id: 'f2_c4',
      name: 'ITB 204',
      displayName: 'ITB 204',
      floor: 2,
      cite: 4,
      isReversed: false,
      keywords: [
        '204',
        'classroom',
        'room',
        'cisco',
        'laboratory',
        'comfort room'
      ],
      aliases: ['room 204', 'classroom 204', 'cisco lab'],
    ),

    // Floor 3
    Building3Location(
      id: 'f3_c0',
      name: 'ITB Third Floor',
      displayName: 'ITB Third Floor',
      floor: 3,
      cite: 0,
      isReversed: false,
      keywords: ['third', 'stairs', 'research', 'laboratory'],
      aliases: ['3rd floor', 'research lab'],
    ),
    Building3Location(
      id: 'f3_c1',
      name: 'ITB 301',
      displayName: 'ITB 301',
      floor: 3,
      cite: 1,
      isReversed: false,
      keywords: ['301', 'classroom', 'room'],
      aliases: ['room 301', 'classroom 301'],
    ),
    Building3Location(
      id: 'f3_c2',
      name: 'ITB 302',
      displayName: 'ITB 302',
      floor: 3,
      cite: 2,
      isReversed: false,
      keywords: ['302', 'classroom', 'room'],
      aliases: ['room 302', 'classroom 302'],
    ),
    Building3Location(
      id: 'f3_c3',
      name: 'Office of the Dean - CITE',
      displayName: 'Office of the Dean - CITE',
      floor: 3,
      cite: 3,
      isReversed: false,
      keywords: ['dean', 'office', 'cite', 'administration'],
      aliases: ['dean office', 'cite dean', 'dean cite'],
    ),
    Building3Location(
      id: 'f3_c4',
      name: 'ITB 304',
      displayName: 'ITB 304',
      floor: 3,
      cite: 4,
      isReversed: false,
      keywords: ['304', 'classroom', 'room', 'faculty', 'comfort room'],
      aliases: ['room 304', 'classroom 304'],
    ),

    // Floor 4
    Building3Location(
      id: 'f4_c0',
      name: 'ITB Fourth Floor',
      displayName: 'ITB Fourth Floor',
      floor: 4,
      cite: 0,
      isReversed: false,
      keywords: ['fourth', 'stairs'],
      aliases: ['4th floor'],
    ),
    Building3Location(
      id: 'f4_c1',
      name: 'ITB 401',
      displayName: 'ITB 401',
      floor: 4,
      cite: 1,
      isReversed: false,
      keywords: ['401', 'classroom', 'room'],
      aliases: ['room 401', 'classroom 401'],
    ),
    Building3Location(
      id: 'f4_c2',
      name: 'ITB 402',
      displayName: 'ITB 402',
      floor: 4,
      cite: 2,
      isReversed: false,
      keywords: ['402', 'classroom', 'room'],
      aliases: ['room 402', 'classroom 402'],
    ),
    Building3Location(
      id: 'f4_c3',
      name: 'ITB 403',
      displayName: 'ITB 403',
      floor: 4,
      cite: 3,
      isReversed: false,
      keywords: ['403', 'classroom', 'room'],
      aliases: ['room 403', 'classroom 403'],
    ),
    Building3Location(
      id: 'f4_c4',
      name: 'ITB 404',
      displayName: 'ITB 404',
      floor: 4,
      cite: 4,
      isReversed: false,
      keywords: ['404', 'classroom', 'room', 'comfort room'],
      aliases: ['room 404', 'classroom 404'],
    ),
  ];

  static List<Building3Location> searchLocations(String query) {
    if (query.isEmpty) return allLocations;

    final lowerQuery = query.toLowerCase();
    return allLocations.where((location) {
      return location.displayName.toLowerCase().contains(lowerQuery) ||
          location.name.toLowerCase().contains(lowerQuery) ||
          location.keywords
              .any((keyword) => keyword.toLowerCase().contains(lowerQuery)) ||
          location.aliases
              .any((alias) => alias.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  static Building3Location? findLocationById(String id) {
    try {
      return allLocations.firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }

  static Building3Location? findLocationByPosition(int floor, int cite) {
    try {
      return allLocations.firstWhere(
        (location) => location.floor == floor && location.cite == cite,
      );
    } catch (e) {
      return null;
    }
  }
}
