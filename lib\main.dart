import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:test_1_copy_auth_events/services/due.dart';
import 'package:test_1_copy_auth_events/pages/login_page.dart';
import 'services/noti_service.dart';
import 'supabase_config.dart';
import 'services/auth_service.dart';
import 'services/bg.dart';
import 'package:test_1_copy_auth_events/services/offline_notification_service.dart';
import 'package:flutter/services.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Supabase
  await SupabaseConfig.initialize();

  // Initialize all notification services
  await DueNotification().initNotifications();
  final taskNotificationService = TaskNotificationService();
  await taskNotificationService.initialize();

  // Initialize the offline notification service
  final offlineNotificationService = OfflineNotificationService();
  await offlineNotificationService.initialize();

  runApp(MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  ThemeMode _themeMode = ThemeMode.light;
  final NotiService _notiService = NotiService();
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadTheme();
    _notiService.initNotification();
    _checkInitialNotifications();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _checkInitialNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final email = prefs.getString('userEmail');
    final isGuest = prefs.getBool('isGuest') ?? false;

    if (email != null) {
      final userDetails = await _authService.fetchUserDetails(email);
      if (userDetails != null) {
        final events = await _authService.fetchEvents(
          userDetails['department'],
          userDetails['course'],
        );
        final tasks = await _authService.fetchUserTasks(email);

        await _showEventNotification(events);
        await Future.delayed(const Duration(seconds: 3));
        await _showOngoingTaskNotification(tasks);
        await Future.delayed(const Duration(seconds: 3));
        await _checkDueTasks(tasks);
      }
    } else if (isGuest) {
      final events = await _authService.fetchEvents('', '', isGuest: true);
      await _showEventNotification(
        events.where((e) => e['forguest'] == 'y').toList(),
      );
    }
  }

  Future<void> _showEventNotification(List events) async {
    int todayEventCount = events.where((event) {
      DateTime eventDate = DateTime.parse(event['date']);
      DateTime currentDate = DateTime.now();
      return eventDate.year == currentDate.year &&
          eventDate.month == currentDate.month &&
          eventDate.day == currentDate.day;
    }).length;

    String eventMessage = todayEventCount > 0
        ? 'You’ve got $todayEventCount event${todayEventCount != 1 ? 's' : ''} lined up for today! 🗓️'
        : 'Enjoy a free day—no events scheduled today!';

    await _notiService.showNotification(
      id: 1,
      title: 'BUtility: Events',
      body: eventMessage,
    );
  }

  Future<void> _showOngoingTaskNotification(List tasks) async {
    int ongoingCount = tasks.where((t) => t['completed'] != 'y').length;

    String message = ongoingCount > 0
        ? 'You’re working on $ongoingCount task${ongoingCount != 1 ? 's' : ''}. You’ve got this! 💪'
        : 'Nice work! All tasks completed.';

    await _notiService.showNotification(
      id: 2,
      title: 'BUtility: Tasks',
      body: message,
    );
  }

  Future<void> _checkDueTasks(List tasks) async {
    final today = DateTime.now();
    final dueTasks = tasks.where((task) {
      if (task['deadline'] == null || task['completed'] == 'y') return false;
      try {
        final deadline = DateTime.parse(task['deadline'].toString());
        return deadline.year == today.year &&
            deadline.month == today.month &&
            deadline.day == today.day;
      } catch (e) {
        return false;
      }
    }).toList();

    if (dueTasks.isNotEmpty) {
      String taskNames = dueTasks.map((t) => t['task']).join(', ');
      String message = dueTasks.length == 1
          ? '"$taskNames" is due today! Please make sure to complete it asap!⏳'
          : 'You’ve got ${dueTasks.length} tasks due today: $taskNames. Let’s do this!';

      await _notiService.showNotification(
        id: 3,
        title: 'BUtility: Due Tasks',
        body: message,
      );
    }
  }

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final isDarkMode = prefs.getBool('isDarkMode') ?? false;
    setState(() {
      _themeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    });
  }

  Future<void> _toggleTheme() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _themeMode =
          _themeMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    });
    await prefs.setBool('isDarkMode', _themeMode == ThemeMode.dark);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Supabase Auth',
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode: _themeMode,
      home: LoginPage(
          toggleTheme: _toggleTheme), // Updated to use Supabase login screen
    );
  }
}
