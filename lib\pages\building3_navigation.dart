import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/image_cache_service.dart';
import '../models/building3_location.dart';
import '../services/building3_pathfinding_service.dart';
import '../widgets/destination_search_dialog.dart';
import '../widgets/navigation_overlay.dart';

class Building3Navigation extends StatefulWidget {
  final bool isGuest;
  final VoidCallback onExit;
  final bool shouldResetState;

  const Building3Navigation({
    Key? key,
    required this.isGuest,
    required this.onExit,
    required this.shouldResetState,
  }) : super(key: key);

  @override
  _Building3NavigationState createState() => _Building3NavigationState();
}

class _Building3NavigationState extends State<Building3Navigation> {
  int _currentCite = 0;
  bool _isReversed = false;
  int _currentFloor = 1;
  bool _isLoading = true;
  String? _currentMapImage;
  final ImageCacheService _imageCacheService = ImageCacheService();
  BuildContext? _safeContext;

  // Navigation state
  NavigationRoute? _activeRoute;
  bool _isNavigating = false;

  @override
  void initState() {
    super.initState();
    _initializeState();

    // Preload images using the cache service after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Check if widget is still mounted
        _imageCacheService.preloadBuildingImages(context, 3);
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Store a reference to the context that can be safely used later
    _safeContext = context;
  }

  Future<void> _initializeState() async {
    if (widget.shouldResetState) {
      setState(() {
        _currentCite = 0;
        _isReversed = false;
        _currentFloor = 1;
        _currentMapImage = _getMapImage();
        _isLoading = false;
      });
      return;
    }

    await _loadNavigationState();
  }

  Future<void> _loadNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    // Load saved state regardless of user type
    final loadedCite = prefs.getInt('${stateKey}_building3_current_cite') ?? 0;
    final loadedReversed =
        prefs.getBool('${stateKey}_building3_is_reversed') ?? false;
    final loadedFloor =
        prefs.getInt('${stateKey}_building3_current_floor') ?? 1;

    setState(() {
      _currentCite = loadedCite;
      _isReversed = loadedReversed;
      _currentFloor = loadedFloor;
      _currentMapImage = _getMapImage();
      _isLoading = false;
    });
  }

  Future<void> _saveNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    // Save state regardless of user type
    await prefs.setInt('${stateKey}_building3_current_cite', _currentCite);
    await prefs.setBool('${stateKey}_building3_is_reversed', _isReversed);
    await prefs.setInt('${stateKey}_building3_current_floor', _currentFloor);
  }

  // Add a method to clear navigation state
  Future<void> _clearNavigationState() async {
    final prefs = await SharedPreferences.getInstance();
    final userEmail = prefs.getString('userEmail') ?? 'guest';
    final stateKey = 'nav_state_$userEmail';

    await prefs.remove('${stateKey}_building3_current_cite');
    await prefs.remove('${stateKey}_building3_is_reversed');
    await prefs.remove('${stateKey}_building3_current_floor');
  }

  @override
  void dispose() {
    // Only save state if we're not resetting
    if (!widget.shouldResetState) {
      _saveNavigationState();
    } else {
      _clearNavigationState();
    }

    // Don't use context in dispose
    _safeContext = null;

    super.dispose();
  }

  String get _locationName {
    switch (_currentFloor) {
      case 1:
        switch (_currentCite) {
          case 0:
            return 'ITB First Floor';
          case 1:
            return 'ITB 101';
          case 2:
            return 'ITB 102';
          case 3:
            return 'ITB 103';
          case 4:
            return 'ITB 104';
          default:
            return 'Unknown Location';
        }
      case 2:
        switch (_currentCite) {
          case 0:
            return 'ITB Second Floor';
          case 1:
            return 'ITB 201';
          case 2:
            return 'ITB 202';
          case 3:
            return 'Center for Career and Counseling';
          case 4:
            return 'ITB 204';
          default:
            return 'Unknown Location';
        }
      case 3:
        switch (_currentCite) {
          case 0:
            return 'ITB Third Floor';
          case 1:
            return 'ITB 301';
          case 2:
            return 'ITB 302';
          case 3:
            return 'Office of the Dean - CITE';
          case 4:
            return 'ITB 304';
          default:
            return 'Unknown Location';
        }
      case 4:
        switch (_currentCite) {
          case 0:
            return 'ITB Fourth Floor';
          case 1:
            return 'ITB 401';
          case 2:
            return 'ITB 402';
          case 3:
            return 'ITB 403';
          case 4:
            return 'ITB 404';
          default:
            return 'Unknown Location';
        }
      default:
        return 'Unknown Location';
    }
  }

  String _getMapImage() {
    if (_currentFloor == 1) {
      if (_currentCite == 0) {
        return 'assets/CITE/cite0.jpg';
      }
      return 'assets/CITE/cite${_currentCite}${_isReversed ? '_r' : ''}.jpg';
    } else {
      if (_currentCite == 0) {
        return 'assets/CITE/${_currentFloor}cite0.jpg';
      }
      return 'assets/CITE/${_currentFloor}cite${_currentCite}${_isReversed ? '_r' : ''}.jpg';
    }
  }

  void _updateMapImage() {
    setState(() {
      _currentMapImage = _getMapImage();
    });
  }

  void _moveForward() {
    setState(() {
      if (_isReversed) {
        if (_currentCite > 1) {
          _currentCite--;
        } else if (_currentCite == 1) {
          _isReversed = false;
          _currentCite = 0;
        }
      } else {
        if (_currentCite < 4) {
          _currentCite++;
        }
      }
      _currentMapImage = _getMapImage(); // Update map image
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _moveBackward() {
    setState(() {
      if (_isReversed) {
        if (_currentCite < 4) {
          _currentCite++;
        }
      } else {
        if (_currentCite > 1) {
          _currentCite--;
        } else if (_currentCite == 1) {
          _currentCite = 0;
          _isReversed =
              false; // Keep it in normal mode when going back to cite0
        }
      }
      _currentMapImage = _getMapImage(); // Update map image
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _toggleReverse() {
    // Don't toggle reverse if we're at cite0 on any floor
    if (_currentCite == 0) {
      return;
    }

    setState(() {
      _isReversed = !_isReversed;
      _currentMapImage = _getMapImage();
      _checkNavigationProgress();
    });
    _saveNavigationState();
  }

  void _moveUpOneFloor() {
    if (_currentFloor < 4) {
      setState(() {
        _currentFloor++;
        // Don't set to reversed when at cite0
        if (_currentCite != 0) {
          _isReversed = true; // Set to reversed when going up
        } else {
          _isReversed = false; // Keep normal view at cite0
        }
        _currentMapImage = _getMapImage(); // Update map image
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  void _moveDownOneFloor() {
    if (_currentFloor > 1) {
      setState(() {
        _currentFloor--;
        // Don't set to reversed when at cite0
        if (_currentCite != 0) {
          _isReversed = true; // Set to reversed when going down
        } else {
          _isReversed = false; // Keep normal view at cite0
        }
        _currentMapImage = _getMapImage(); // Update map image
        _checkNavigationProgress();
      });
      _saveNavigationState();
    }
  }

  Future<void> _precacheImages() async {
    // Precache all possible floor images
    for (int floor = 1; floor <= 4; floor++) {
      String floorPrefix = floor > 1 ? floor.toString() : '';
      // Precache main floor image
      await precacheImage(
          AssetImage('assets/CITE/${floorPrefix}cite0.jpg'), context);

      // Precache all cite images for this floor
      for (int cite = 1; cite <= 4; cite++) {
        await precacheImage(
            AssetImage('assets/CITE/${floorPrefix}cite${cite}.jpg'), context);
        await precacheImage(
            AssetImage('assets/CITE/${floorPrefix}cite${cite}_r.jpg'), context);
      }
    }
  }

  List<String> _getLocationInfoList() {
    List<String> infoList = [];

    if (_currentFloor == 1) {
      if (_currentCite == 0) {
        infoList.add('Power Room');
        infoList.add('Cultural and Sports Center');
      }
      if (_currentCite == 4) {
        infoList.add('Comfort Room');
      }
    } else if (_currentFloor == 2) {
      if (_currentCite == 0) {
        infoList.add('CITS (Computer and Information Technology Services)');
      }
      if (_currentCite == 0 || _currentCite == 1) {
        infoList.add('Mac Laboratory');
      }
      if (_currentCite == 3 || _currentCite == 4) {
        infoList.add('Cisco Laboratory');
      }
      if (_currentCite == 4) {
        infoList.add('Comfort Room');
      }
    } else if (_currentFloor == 3) {
      if (_currentCite == 0) {
        infoList.add('Research Laboratory');
      }
      if (_currentCite == 4) {
        infoList.add('Faculty Comfort Room');
      }
    } else if (_currentFloor == 4) {
      if (_currentCite == 4) {
        infoList.add('Comfort Room');
      }
    }

    if (infoList.isEmpty) {
      infoList.add('Additional information not available for this location');
    }

    return infoList;
  }

  void _showLocationInfo() {
    List<String> infoList = _getLocationInfoList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              SizedBox(width: 8),
              Text(
                'Location Information',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Container(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: infoList.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 24),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(infoList[index]),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  // Navigation methods
  void _showDestinationSearch() {
    final currentLocation = Building3LocationData.findLocationByPosition(
        _currentFloor, _currentCite);
    if (currentLocation == null) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return DestinationSearchDialog(
          currentLocation: currentLocation,
          onRouteSelected: _startNavigation,
          isCurrentlyReversed: _isReversed,
        );
      },
    );
  }

  void _startNavigation(NavigationRoute route) {
    setState(() {
      _activeRoute = route;
      _isNavigating = true;
    });
  }

  void _stopNavigation() {
    setState(() {
      _activeRoute = null;
      _isNavigating = false;
    });
  }

  void _onNavigationStepCompleted(NavigationStep step) {
    // This method can be used to track navigation progress
    // For now, we'll just update the UI when the user moves
  }

  Building3Location? get _currentLocation {
    return Building3LocationData.findLocationByPosition(
        _currentFloor, _currentCite);
  }

  void _checkNavigationProgress() {
    if (!_isNavigating || _activeRoute == null || _currentLocation == null) {
      return;
    }

    // Check if user has arrived at destination
    if (_currentLocation!.floor == _activeRoute!.destination.floor &&
        _currentLocation!.cite == _activeRoute!.destination.cite) {
      // User has arrived, stop navigation after a short delay
      Future.delayed(Duration(seconds: 3), () {
        if (mounted && _isNavigating) {
          _stopNavigation();
        }
      });
      return;
    }

    // Check if user is following the route correctly
    final currentStep = _activeRoute!.getCurrentStep(_currentLocation!);
    if (currentStep != null) {
      // User is on the correct path, trigger step completion callback
      _onNavigationStepCompleted(currentStep);
    }
  }

  @override
  void didUpdateWidget(Building3Navigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check navigation progress when the widget updates
    if (_isNavigating) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _checkNavigationProgress();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            color: Colors.green,
          ),
        ),
      );
    }

    // Calculate device dimensions for optimal caching
    final Size deviceSize = MediaQuery.of(context).size;
    final int cacheWidth = (deviceSize.width * 1.5).toInt();
    final int cacheHeight = (deviceSize.height * 1.5).toInt();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Building 3 Navigation',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.white,
          onPressed: widget.onExit,
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            color: Colors.white,
            onPressed: _showDestinationSearch,
            tooltip: 'Search Destination',
          ),
          IconButton(
            icon: Icon(Icons.info_outline),
            color: Colors.white,
            onPressed: _showLocationInfo,
            tooltip: 'Location Information',
          ),
        ],
      ),
      body: Stack(
        children: [
          AnimatedSwitcher(
            duration: Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            child: Image.asset(
              _currentMapImage!,
              key: ValueKey(_currentMapImage),
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.fill,
              cacheWidth: cacheWidth,
              cacheHeight: cacheHeight,
              gaplessPlayback: true,
            ),
          ),

          // Navigation overlay
          if (_isNavigating && _activeRoute != null && _currentLocation != null)
            NavigationOverlay(
              route: _activeRoute!,
              currentLocation: _currentLocation!,
              onStopNavigation: _stopNavigation,
              onStepCompleted: _onNavigationStepCompleted,
            ),
          // Location overlay (hidden during navigation)
          if (!_isNavigating)
            Positioned(
              top: 16,
              left: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          _locationName,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // Eye icon for Sports and Cultural Center (only on 1st floor cite0)
                      if (_currentFloor == 1 && _currentCite == 0)
                        Container(
                          margin: EdgeInsets.only(left: 8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: IconButton(
                            icon: Icon(Icons.visibility, color: Colors.white),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return Dialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.2),
                                            spreadRadius: 2,
                                            blurRadius: 5,
                                            offset: Offset(0, 3),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ClipRRect(
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(20),
                                              topRight: Radius.circular(20),
                                            ),
                                            child: Image.asset(
                                              'assets/IMAGES/csc.jpg',
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8.0),
                                            child: Text(
                                              'Cultural and Sports Center',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18,
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? Colors.black
                                                    : Theme.of(context)
                                                        .textTheme
                                                        .bodyLarge
                                                        ?.color,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(5.0),
                                            child: TextButton(
                                              onPressed: () =>
                                                  Navigator.of(context).pop(),
                                              child: Text('Close',
                                                  style: TextStyle(
                                                      color: Colors.green,
                                                      fontSize: 16)),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      // Eye icon for Clinic (only on 1st floor cite2)
                      if (_currentFloor == 1 && _currentCite == 2)
                        Container(
                          margin: EdgeInsets.only(left: 8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: IconButton(
                            icon: Icon(Icons.visibility, color: Colors.white),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return Dialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(20),
                                        boxShadow: [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.2),
                                            spreadRadius: 2,
                                            blurRadius: 5,
                                            offset: Offset(0, 3),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ClipRRect(
                                            borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(20),
                                              topRight: Radius.circular(20),
                                            ),
                                            child: Image.asset(
                                              'assets/IMAGES/clinic.jpg',
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 8.0),
                                            child: Text(
                                              'Medical and Dental Clinic',
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 18,
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? Colors.black
                                                    : null,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(5.0),
                                            child: TextButton(
                                              onPressed: () =>
                                                  Navigator.of(context).pop(),
                                              child: Text('Close',
                                                  style: TextStyle(
                                                      color: Colors.green,
                                                      fontSize: 16)),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                  if (_currentCite == 3 && _currentFloor == 1)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: IconButton(
                          icon: Icon(Icons.visibility, color: Colors.white),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return Dialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.2),
                                          spreadRadius: 2,
                                          blurRadius: 5,
                                          offset: Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ClipRRect(
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(20),
                                            topRight: Radius.circular(20),
                                          ),
                                          child: Image.asset(
                                            'assets/IMAGES/clinic.jpg',
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          child: Text(
                                            'Medical and Dental Clinic',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                              color: Theme.of(context)
                                                          .brightness ==
                                                      Brightness.dark
                                                  ? Colors.black
                                                  : null,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(5.0),
                                          child: TextButton(
                                            onPressed: () =>
                                                Navigator.of(context).pop(),
                                            child: Text('Close',
                                                style: TextStyle(
                                                    color: Colors.green,
                                                    fontSize: 16)),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),
            ),
          // Reversed/Normal icon overlay (hidden during navigation)
          if (!_isNavigating)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _isReversed ? Colors.yellow : Colors.grey,
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.autorenew,
                  color: _isReversed ? Colors.yellow : Colors.grey,
                  size: 24,
                ),
              ),
            ),

          // Show 'Stairs on the left' at the top for cite0 on any floor (hidden during navigation)
          if (_currentCite == 0 && !_isNavigating)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Stairs on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ),
            ),

          // Medical and Dental Clinic on the right message (hidden during navigation)
          if (!_isReversed &&
              (_currentFloor == 1 && _currentCite == 2) &&
              !_isNavigating)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Medical and Dental Clinic on the right',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Medical and Dental Clinic on the left message (hidden during navigation)
          if (_isReversed &&
              (_currentFloor == 1 && _currentCite == 2) &&
              !_isNavigating)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Medical and Dental Clinic on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Comfort room on the left message (hidden during navigation)
          if ((((!_isReversed && (_currentFloor == 1 && _currentCite == 4) ||
                  (!_isReversed && (_currentFloor == 2 && _currentCite == 4) ||
                      (!_isReversed &&
                              (_currentFloor == 3 && _currentCite == 4) ||
                          (!_isReversed &&
                              (_currentFloor == 4 && _currentCite == 4))))))) &&
              !_isNavigating)
            Positioned(
              top: 100,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Comfort room on the left',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                      shadows: [Shadow(color: Colors.black, blurRadius: 2)],
                    ),
                  ),
                ),
              ),
            ),

          // Floor navigation buttons - responsive positioning
          if ((_currentCite == 0 && (_currentFloor < 4 || _currentFloor > 1)) ||
              (_currentCite == 4 &&
                  !_isReversed &&
                  (_currentFloor < 4 || _currentFloor > 1)))
            Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Down Floor Button (left side)
                  if ((_currentCite == 0 && _currentFloor > 1) ||
                      (_currentCite == 4 && !_isReversed && _currentFloor > 1))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'downBtn',
                          onPressed: _moveDownOneFloor,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child: Transform.rotate(
                            angle: 3.14159, // 180 degrees in radians (π)
                            child: Icon(Icons.stairs_outlined,
                                color: Colors.black),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text('Go Down',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),

                  // Spacing between buttons
                  SizedBox(width: 40),

                  // Up Floor Button (right side)
                  if ((_currentCite == 0 && _currentFloor < 4) ||
                      (_currentCite == 4 && !_isReversed && _currentFloor < 4))
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        FloatingActionButton(
                          heroTag: 'upBtn',
                          onPressed: _moveUpOneFloor,
                          backgroundColor: Colors.white.withOpacity(0.7),
                          child:
                              Icon(Icons.stairs_outlined, color: Colors.black),
                        ),
                        SizedBox(height: 4),
                        Text('Go Up',
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                shadows: [
                                  Shadow(color: Colors.black, blurRadius: 2)
                                ])),
                      ],
                    ),
                ],
              ),
            ),
          // Main navigation buttons (bottom right, vertical stack)
          Positioned(
            bottom: 40,
            right: 16,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Forward (Up Arrow)
                if (!(_currentCite == 4 && !_isReversed))
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'forwardBtn',
                        onPressed: _moveForward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child:
                            Icon(Icons.keyboard_arrow_up, color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Forward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),
                SizedBox(height: 20),
                // Reverse (Swap)
                if (_currentCite != 0)
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'reverseBtn',
                        onPressed: _toggleReverse,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.autorenew,
                            color: Colors.black, size: 30),
                      ),
                      SizedBox(height: 4),
                      Text('Reverse',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),
                SizedBox(height: 20),
                // Backward (Down Arrow)
                if (_currentCite == 0)
                  SizedBox.shrink()
                else if (!(_currentCite == 4 && _isReversed))
                  Column(
                    children: [
                      FloatingActionButton(
                        heroTag: 'backBtn',
                        onPressed: _moveBackward,
                        backgroundColor: Colors.white.withOpacity(0.7),
                        child: Icon(Icons.keyboard_arrow_down,
                            color: Colors.black),
                      ),
                      SizedBox(height: 4),
                      Text('Backward',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(color: Colors.black, blurRadius: 2)
                              ])),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
