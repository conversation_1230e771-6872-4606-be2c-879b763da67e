import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';

class ImageCacheService {
  static final ImageCacheService _instance = ImageCacheService._internal();
  factory ImageCacheService() => _instance;
  ImageCacheService._internal() {
    // Increase cache size on initialization
    PaintingBinding.instance.imageCache.maximumSize =
        300; // Increase from 200 to 300
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        200 * 1024 * 1024; // 200 MB
  }

  bool _hasPreloadedBuilding1 = false;
  bool _hasPreloadedBuilding2 = false;
  bool _hasPreloadedBuilding3 = false;

  // Store preloaded image futures to avoid duplicate loading
  final Map<String, Future<void>> _preloadedImages = {};

  Future<void> preloadBuildingImages(
      BuildContext context, int buildingNumber) async {
    // Don't use context if the widget might be disposed
    if (!context.mounted) return;

    final prefs = await SharedPreferences.getInstance();
    final cacheKey = 'image_cache_building$buildingNumber';
    final hasPreloaded = prefs.getBool(cacheKey) ?? false;

    // Skip if already preloaded in this session
    if (buildingNumber == 1 && _hasPreloadedBuilding1) return;
    if (buildingNumber == 2 && _hasPreloadedBuilding2) return;
    if (buildingNumber == 3 && _hasPreloadedBuilding3) return;

    // If we've preloaded before, increase the cache size but don't reload all images
    if (hasPreloaded) {
      // Increase the image cache size to keep more images in memory
      PaintingBinding.instance.imageCache.maximumSize = 200;

      if (buildingNumber == 1) _hasPreloadedBuilding1 = true;
      if (buildingNumber == 2) _hasPreloadedBuilding2 = true;
      if (buildingNumber == 3) _hasPreloadedBuilding3 = true;
      return;
    }

    // Preload images based on building number
    switch (buildingNumber) {
      case 1:
        await _preloadBuilding1Images(context);
        _hasPreloadedBuilding1 = true;
        break;
      case 2:
        await _preloadBuilding2Images(context);
        _hasPreloadedBuilding2 = true;
        break;
      case 3:
        await _preloadBuilding3Images(context);
        _hasPreloadedBuilding3 = true;
        break;
    }

    // Mark as preloaded
    await prefs.setBool(cacheKey, true);
  }

  // Helper method to safely precache an image
  Future<void> _safelyPrecacheImage(
      ImageProvider imageProvider, BuildContext context) async {
    if (!context.mounted) return;

    final String key = imageProvider.toString();
    if (_preloadedImages.containsKey(key)) {
      return _preloadedImages[key];
    }

    final Future<void> loadFuture = precacheImage(imageProvider, context);
    _preloadedImages[key] = loadFuture;
    return loadFuture;
  }

  Future<void> _preloadBuilding1Images(BuildContext context) async {
    // Check if context is still valid
    if (!context.mounted) return;

    // Increase the image cache size
    PaintingBinding.instance.imageCache.maximumSize = 200;

    // Preload main building images
    for (int main = 0; main <= 23; main++) {
      if (!context.mounted) return; // Check again in the loop
      await _safelyPrecacheImage(
          AssetImage('assets/MAIN/main$main.jpg'), context);
      if (main != 0 && main != 16) {
        if (!context.mounted) return; // Check again in the loop
        await _safelyPrecacheImage(
            AssetImage('assets/MAIN/main${main}_r.jpg'), context);
      }
    }
  }

  Future<void> _preloadBuilding2Images(BuildContext context) async {
    // Check if context is still valid
    if (!context.mounted) return;

    // Increase the image cache size
    PaintingBinding.instance.imageCache.maximumSize = 200;

    // Preload CLAGE building images
    for (int floor = 1; floor <= 4; floor++) {
      if (!context.mounted) return; // Check again in the loop
      String floorPrefix = floor > 1 ? floor.toString() : '';
      await _safelyPrecacheImage(
          AssetImage('assets/CLAGE/${floorPrefix}clage0.jpg'), context);

      int maxClage = floor <= 2 ? 10 : 9;
      for (int clage = 1; clage <= maxClage; clage++) {
        if (!context.mounted) return; // Check again in the loop
        await _safelyPrecacheImage(
            AssetImage('assets/CLAGE/${floorPrefix}clage${clage}.jpg'),
            context);
        await _safelyPrecacheImage(
            AssetImage('assets/CLAGE/${floorPrefix}clage${clage}_r.jpg'),
            context);
      }
    }
  }

  Future<void> _preloadBuilding3Images(BuildContext context) async {
    // Check if context is still valid
    if (!context.mounted) return;

    // Increase the image cache size
    PaintingBinding.instance.imageCache.maximumSize = 200;

    // Preload CITE building images
    for (int floor = 1; floor <= 4; floor++) {
      if (!context.mounted) return; // Check again in the loop
      String floorPrefix = floor > 1 ? floor.toString() : '';
      await _safelyPrecacheImage(
          AssetImage('assets/CITE/${floorPrefix}cite0.jpg'), context);

      for (int cite = 1; cite <= 4; cite++) {
        if (!context.mounted) return; // Check again in the loop
        await _safelyPrecacheImage(
            AssetImage('assets/CITE/${floorPrefix}cite${cite}.jpg'), context);
        await _safelyPrecacheImage(
            AssetImage('assets/CITE/${floorPrefix}cite${cite}_r.jpg'), context);
      }
    }
  }
}
